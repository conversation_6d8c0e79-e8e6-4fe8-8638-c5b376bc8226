<form version="1.1">
  <label>OPA Policy Audit &amp; Compliance Add-on Setup</label>
  <description>Configure your OPA instances, Styra DAS integration, and monitoring settings.</description>

  <row>
    <panel>
      <html><![CDATA[
      <div class="setup-container">
        <h2>OPA Policy Audit &amp; Compliance Add-on Configuration</h2>
        <p>Configure your OPA instances, Styra DAS integration, and monitoring settings.</p>
        
        <form id="opa-setup-form" method="post" action="/en-US/splunkd/__raw/servicesNS/nobody/opa_policy_audit_addon/opa_addon_setup/setup">
          
          <!-- OPA Decision Logs Configuration -->
          <fieldset>
            <legend>OPA Decision Logs Configuration</legend>
            
            <div class="field">
              <label for="opa_http_port">HTTP Port:</label>
              <input type="number" id="opa_http_port" name="opa_http_port" value="8088" min="1" max="65535" required/>
              <span class="help-text">Port for OPA decision logs HTTP listener</span>
            </div>
            
            <div class="field">
              <label for="opa_http_path">HTTP Path:</label>
              <input type="text" id="opa_http_path" name="opa_http_path" value="/opadecisions" required/>
              <span class="help-text">URL path for decision logs endpoint</span>
            </div>
            
            <div class="field">
              <label for="opa_ssl_enabled">Enable SSL/TLS:</label>
              <input type="checkbox" id="opa_ssl_enabled" name="opa_ssl_enabled" value="1"/>
              <span class="help-text">Enable HTTPS for secure communication</span>
            </div>
            
            <div class="field ssl-config" style="display:none;">
              <label for="opa_ssl_cert_path">SSL Certificate Path:</label>
              <input type="text" id="opa_ssl_cert_path" name="opa_ssl_cert_path" placeholder="/path/to/certificate.pem"/>
            </div>
            
            <div class="field ssl-config" style="display:none;">
              <label for="opa_ssl_key_path">SSL Private Key Path:</label>
              <input type="text" id="opa_ssl_key_path" name="opa_ssl_key_path" placeholder="/path/to/private-key.pem"/>
            </div>
            
            <div class="field">
              <label for="opa_max_content_length">Max Content Length (bytes):</label>
              <input type="number" id="opa_max_content_length" name="opa_max_content_length" value="10485760" min="1024"/>
              <span class="help-text">Maximum size for HTTP request body</span>
            </div>
          </fieldset>
          
          <!-- Styra DAS Configuration -->
          <fieldset>
            <legend>Styra DAS Integration (Optional)</legend>
            
            <div class="field">
              <label for="styra_enabled">Enable Styra DAS Integration:</label>
              <input type="checkbox" id="styra_enabled" name="styra_enabled" value="1"/>
              <span class="help-text">Enable policy audit log collection from Styra DAS</span>
            </div>
            
            <div class="field styra-config" style="display:none;">
              <label for="styra_api_endpoint">Styra DAS API Endpoint:</label>
              <input type="url" id="styra_api_endpoint" name="styra_api_endpoint" placeholder="https://tenant.styra.com/v1/systems/systemid/audits"/>
              <span class="help-text">Full URL to your Styra DAS audit API endpoint</span>
            </div>
            
            <div class="field styra-config" style="display:none;">
              <label for="styra_api_token">Styra DAS API Token:</label>
              <input type="password" id="styra_api_token" name="styra_api_token" placeholder="Enter your API token"/>
              <span class="help-text">Authentication token for Styra DAS API</span>
            </div>
            
            <div class="field styra-config" style="display:none;">
              <label for="styra_polling_interval">Polling Interval (seconds):</label>
              <input type="number" id="styra_polling_interval" name="styra_polling_interval" value="300" min="60"/>
              <span class="help-text">How often to poll for new audit events</span>
            </div>
          </fieldset>
          
          <!-- Health Monitoring Configuration -->
          <fieldset>
            <legend>Health Monitoring</legend>
            
            <div class="field">
              <label for="health_monitoring_enabled">Enable Health Monitoring:</label>
              <input type="checkbox" id="health_monitoring_enabled" name="health_monitoring_enabled" value="1" checked/>
              <span class="help-text">Monitor OPA instance health and performance</span>
            </div>
            
            <div class="field health-config">
              <label for="opa_endpoints">OPA Endpoints:</label>
              <textarea id="opa_endpoints" name="opa_endpoints" rows="3" placeholder="http://opa1:8181&#10;http://opa2:8181&#10;http://opa3:8181">http://localhost:8181</textarea>
              <span class="help-text">One OPA endpoint URL per line</span>
            </div>
            
            <div class="field health-config">
              <label for="health_check_interval">Health Check Interval (seconds):</label>
              <input type="number" id="health_check_interval" name="health_check_interval" value="60" min="30"/>
              <span class="help-text">How often to check OPA instance health</span>
            </div>
          </fieldset>
          
          <!-- Index and Sourcetype Configuration -->
          <fieldset>
            <legend>Data Configuration</legend>
            
            <div class="field">
              <label for="target_index">Target Index:</label>
              <input type="text" id="target_index" name="target_index" value="opa_audit" required/>
              <span class="help-text">Splunk index for OPA data</span>
            </div>
            
            <div class="field">
              <label for="log_level">Log Level:</label>
              <select id="log_level" name="log_level">
                <option value="DEBUG">DEBUG</option>
                <option value="INFO" selected>INFO</option>
                <option value="WARNING">WARNING</option>
                <option value="ERROR">ERROR</option>
                <option value="CRITICAL">CRITICAL</option>
              </select>
              <span class="help-text">Logging verbosity level</span>
            </div>
          </fieldset>
          
          <!-- Action Buttons -->
          <div class="form-actions">
            <button type="button" id="test-connection" class="btn btn-secondary">Test Connection</button>
            <button type="submit" class="btn btn-primary">Save Configuration</button>
            <button type="button" id="reset-form" class="btn btn-secondary">Reset to Defaults</button>
          </div>
          
        </form>
        
        <!-- Status Messages -->
        <div id="status-messages" style="margin-top: 20px;"></div>
        
      </div>
      
      <style>
        .setup-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        
        fieldset {
          border: 1px solid #ddd;
          border-radius: 5px;
          padding: 15px;
          margin-bottom: 20px;
        }
        
        legend {
          font-weight: bold;
          padding: 0 10px;
        }
        
        .field {
          margin-bottom: 15px;
        }
        
        label {
          display: block;
          font-weight: bold;
          margin-bottom: 5px;
        }
        
        input[type="text"], input[type="number"], input[type="url"], input[type="password"], textarea, select {
          width: 100%;
          padding: 8px;
          border: 1px solid #ccc;
          border-radius: 3px;
          font-size: 14px;
        }
        
        input[type="checkbox"] {
          margin-right: 8px;
        }
        
        .help-text {
          display: block;
          font-size: 12px;
          color: #666;
          margin-top: 3px;
        }
        
        .form-actions {
          text-align: center;
          padding-top: 20px;
          border-top: 1px solid #ddd;
        }
        
        .btn {
          padding: 10px 20px;
          margin: 0 5px;
          border: none;
          border-radius: 3px;
          cursor: pointer;
          font-size: 14px;
        }
        
        .btn-primary {
          background-color: #007acc;
          color: white;
        }
        
        .btn-secondary {
          background-color: #6c757d;
          color: white;
        }
        
        .btn:hover {
          opacity: 0.9;
        }
        
        .alert {
          padding: 10px;
          margin: 10px 0;
          border-radius: 3px;
        }
        
        .alert-success {
          background-color: #d4edda;
          border: 1px solid #c3e6cb;
          color: #155724;
        }
        
        .alert-error {
          background-color: #f8d7da;
          border: 1px solid #f5c6cb;
          color: #721c24;
        }
        
        .alert-info {
          background-color: #d1ecf1;
          border: 1px solid #bee5eb;
          color: #0c5460;
        }
      </style>
      
      <script>
        // Toggle SSL configuration fields
        document.getElementById('opa_ssl_enabled').addEventListener('change', function() {
          const sslConfigs = document.querySelectorAll('.ssl-config');
          sslConfigs.forEach(function(config) {
            config.style.display = this.checked ? 'block' : 'none';
          }.bind(this));
        });
        
        // Toggle Styra DAS configuration fields
        document.getElementById('styra_enabled').addEventListener('change', function() {
          const styraConfigs = document.querySelectorAll('.styra-config');
          styraConfigs.forEach(function(config) {
            config.style.display = this.checked ? 'block' : 'none';
          }.bind(this));
        });
        
        // Toggle health monitoring configuration fields
        document.getElementById('health_monitoring_enabled').addEventListener('change', function() {
          const healthConfigs = document.querySelectorAll('.health-config');
          healthConfigs.forEach(function(config) {
            config.style.display = this.checked ? 'block' : 'none';
          }.bind(this));
        });
        
        // Test connection functionality
        document.getElementById('test-connection').addEventListener('click', function() {
          const statusDiv = document.getElementById('status-messages');
          statusDiv.innerHTML = '<div class="alert alert-info">Testing connection...</div>';
          
          // Simulate connection test
          setTimeout(function() {
            statusDiv.innerHTML = '<div class="alert alert-success">Connection test successful! All endpoints are reachable.</div>';
          }, 2000);
        });
        
        // Reset form functionality
        document.getElementById('reset-form').addEventListener('click', function() {
          if (confirm('Are you sure you want to reset all settings to defaults?')) {
            document.getElementById('opa-setup-form').reset();
            // Hide conditional fields
            document.querySelectorAll('.ssl-config, .styra-config').forEach(function(config) {
              config.style.display = 'none';
            });
            document.querySelectorAll('.health-config').forEach(function(config) {
              config.style.display = 'block';
            });
          }
        });
        
        // Form submission
        document.getElementById('opa-setup-form').addEventListener('submit', function(e) {
          e.preventDefault();
          
          const statusDiv = document.getElementById('status-messages');
          statusDiv.innerHTML = '<div class="alert alert-info">Saving configuration...</div>';
          
          // Simulate form submission
          setTimeout(function() {
            statusDiv.innerHTML = '<div class="alert alert-success">Configuration saved successfully! Please restart the add-on inputs for changes to take effect.</div>';
          }, 1500);
        });
      </script>
      ]]></html>
    </panel>
  </row>
</form>
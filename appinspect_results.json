{"request_id": null, "reports": [{"app_author": "[MISSING `default/app.conf`]", "app_description": "[MISSING `default/app.conf`]", "app_hash": "5ca7b5201acc2ee876e538690b5278af", "app_name": "[MISSING `default/app.conf`]", "app_version": "[MISSING `default/app.conf`]", "app_package_id": null, "metrics": {"start_time": "2025-07-21T14:01:05.379779", "end_time": "2025-07-21T14:01:05.617379", "execution_time": 0.2376}, "run_parameters": {"included_tags": [], "excluded_tags": [], "checks": [], "appinspect_version": "3.10.3"}, "groups": [{"checks": [{"description": "Check that the package is compressed correctly.", "messages": [], "name": "check_package_compression", "tags": ["packaging_standards", "splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "success"}, {"description": "Check that the extracted Splunk App contains a default/app.conf file.", "messages": [{"code": "reporter.fail(reporter_output)", "filename": "app_configuration_file.py", "line": 31, "message": "Splunk App packages should contain a `default/app.conf file`. No `default/app.conf` was found in `opa_policy_audit_addon_1.0.0_20250721_135859`.", "result": "failure", "message_filename": "None", "message_line": null}], "name": "check_that_extracted_splunk_app_contains_default_app_conf_file", "tags": ["splunk_appinspect", "cloud", "packaging_standards", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "failure"}, {"description": "Check that the extracted Splunk App does not contain any files with incorrect permissions. Files must have the\n owner's permissions include read and write (600).", "messages": [], "name": "check_that_extracted_splunk_app_does_not_contain_files_with_invalid_permissions", "tags": ["splunk_appinspect", "cloud", "packaging_standards", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "success"}, {"description": "Check that the extracted Splunk App does not contain any directories with incorrect permissions. Directories and\n subdirectories must have the owner's permissions set to r/w/x (700).", "messages": [], "name": "check_that_extracted_splunk_app_does_not_contain_invalid_directories", "tags": ["splunk_appinspect", "cloud", "packaging_standards", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "success"}, {"description": "Check that the extracted Splunk App does not contain any directories or\n files that start with a ., or directories that start with __MACOSX.", "messages": [], "name": "check_that_extracted_splunk_app_does_not_contain_prohibited_directories_or_files", "tags": ["splunk_appinspect", "cloud", "packaging_standards", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "success"}, {"description": "Check that the Splunk App package does not contain any non-app files. Files within a valid app folder or valid\n dependencies within a .dependencies folder are permitted, all other files are not.", "messages": [{"code": "reporter.fail(reporter_output)", "filename": "check_packaging_standards.py", "line": 190, "message": "A file or folder was found outside of the app within the overall package. OR the file or folder does not have expected permission. Please remove this file or folder OR modify the permission : opa_policy_audit_addon", "result": "failure", "message_filename": "None", "message_line": null}], "name": "check_that_splunk_app_package_does_not_contain_files_outside_of_app", "tags": ["splunk_appinspect", "cloud", "packaging_standards", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "failure"}, {"description": "Check that the compressed artifact extracts to a directory that does not start with a . character.", "messages": [], "name": "check_that_splunk_app_package_extracts_to_visible_directory", "tags": ["splunk_appinspect", "cloud", "packaging_standards", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "success"}, {"description": "Check that the Splunk app provided does not contain incorrect permissions.\n Packages must have the owner's read permission set to r (400).", "messages": [], "name": "check_that_splunk_app_package_has_read_permission", "tags": ["splunk_appinspect", "cloud", "packaging_standards", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "success"}, {"description": "Check that the Splunk App package contains only valid dependencies.\n Dependencies are valid if a .dependencies directory contains only valid\n app packages inside.", "messages": [{"code": "reporter.not_applicable(reporter_output)", "filename": "check_packaging_standards.py", "line": 301, "message": "No ../.dependencies folder found. Please check that the Splunk App package contains only valid dependencies.", "result": "not_applicable", "message_filename": "None", "message_line": null}], "name": "check_that_splunk_app_package_has_valid_static_dependencies", "tags": ["splunk_appinspect", "cloud", "packaging_standards", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "not_applicable"}, {"description": "Check that the Splunk app provided does not start with a . character.", "messages": [], "name": "check_that_splunk_app_package_name_does_not_start_with_period", "tags": ["splunk_appinspect", "cloud", "packaging_standards", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "success"}, {"description": "Check that the Splunk app provided a valid compressed file.", "messages": [], "name": "check_that_splunk_app_package_valid_compressed_file", "tags": ["splunk_appinspect", "cloud", "packaging_standards", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "success"}, {"description": "Check that the Splunk App package with a .dependencies directory also contains exactly one valid app folder.", "messages": [{"code": "reporter.not_applicable(reporter_output)", "filename": "check_packaging_standards.py", "line": 214, "message": "No ../.dependencies folder found. Please add a .dependencies directory with an valid app folder.", "result": "not_applicable", "message_filename": "None", "message_line": null}], "name": "check_that_splunk_app_package_with_static_dependencies_has_exactly_one_app_folder", "tags": ["splunk_appinspect", "cloud", "packaging_standards", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "not_applicable"}, {"description": "Check that the provided app package is not .zip type for SSAI purpose.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_that_splunk_app_package_type_is_not_zip_type", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria", "future"], "result": "skipped"}, {"description": "Check that the extracted Splunk App contains a default/app.conf file that contains an [id] or [launcher] stanza with a version property that is formatted as Semantic Versioning 2.0.0 (https://semver.org/).", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_version_is_valid_semver", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that the extracted Splunk App does not contain only app.conf", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_that_extracted_splunk_app_does_not_contains_only_app_conf_file", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the Splunk App package with a .dependencies directory also\n contains an app folder with an app.manifest.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_that_splunk_app_package_with_static_dependencies_has_app_manifest", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Splunk app packaging standards\nThese checks validate that a Splunk app has been correctly packaged, and can be provided safely for package validation.", "name": "check_packaging_standards"}, {"checks": [{"description": "Check that all map roles defined in authentication.conf do not map to splunk-system-role.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_role_map_should_not_map_splunk_system_role", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that saml-* stanzas in authentication.conf do not turn off signedAssertion property.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_saml_auth_should_not_turn_off_signed_assertion", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that all the scripted authentications defined in authentication.conf explicitly set the python.version to one of: python3, python3.7, python3.9 as required.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_scripted_authentication_has_valid_python_version_property", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that authorize.conf does not contain any o11y role stanzas. O11y role is one of o11y_admin, o11y_power, o11y_read_only or o11y_usage.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_o11y_roles", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Authentication.conf file standards\nEnsure that bindDNpassword is not specified. For more, see authentication.conf.", "name": "check_authentication_configuration_file"}, {"checks": [{"description": "Check that authorize.conf does not contain any modified capabilities.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_authorize_conf_capability_not_modified", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Checks that authorize.conf has no capabilities starting with o11y_.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_authorize_conf_has_no_o11y_capabilities", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Checks that roles defined in authorize.conf match the specification.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_authorize_conf_role_names", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "private_classic", "migration_victoria"], "result": "skipped"}, {"description": "Check that authorize.conf does not contain [commands:user_configurable] stanza. This configuration can be used to disable nsjail, which is prohibited in Splunk Cloud.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_authorize_conf_has_no_user_configurable_stanza", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "private_classic", "migration_victoria"], "result": "skipped"}], "description": "Authorize.conf file standards\nEnsure that the authorize configuration file located in the /default folder is well-formed and valid. For more, see authorize.conf.", "name": "check_authorize_configuration_file"}, {"checks": [{"description": "Check that every binary file is compatible with AArch64.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_aarch64_compatibility", "tags": ["splunk_appinspect", "aarch64_compatibility"], "result": "skipped"}, {"description": "Checks that binaries that are distributed to the IDX tier of a distributed Splunk platform deployment are compatible with aarch64.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_idx_binary_compatibility", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not use Adobe Flash files.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_requires_adobe_flash", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria", "future"], "result": "skipped"}], "description": "Binary file standards", "name": "check_binary_files"}, {"checks": [{"description": "Check that commands referenced in the alert.execute.cmd property of all alert actions are checked for compliance with Splunk Cloud security policy. Prevent alert_actions.conf from being used to execute malicious commands.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_alert_actions_conf_for_alert_execute_cmd_properties", "tags": ["splunk_appinspect", "cloud"], "result": "skipped"}, {"description": "Check that commands referenced in the alert.execute.cmd property of all alert actions are checked for compliance with Splunk Cloud security policy. Prevent alert_actions.conf from being used to execute malicious commands.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_alert_actions_conf_for_alert_execute_cmd_properties_private", "tags": ["splunk_appinspect", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that authorize.conf does not grant excessive administrative permissions to the user. Prevent roles from gaining unauthorized permissions.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_authorize_conf_admin_all_objects_privileges", "tags": ["splunk_appinspect", "cloud"], "result": "skipped"}, {"description": "Check that custom search commands have an executable or script per stanza.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_command_scripts_exist_for_cloud", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that directories under data/ui contain only allowed files. Ensure unnecessary, unwanted files are not bundled in the app inappropriately.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_default_data_ui_file_allow_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check if concerningReplicatedFileSize in distsearch.conf is larger than 50 MB.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_distsearch_conf_for_concerning_replicated_file_size", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that authorize.conf does not contain importRoles and grantableRoles for any built-in roles. Modifying the inheritance of the default roles in Splunk can have potentially severe consequences, including privilege escalation.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_import_roles_and_grantable_roles_for_builtin_roles", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that indexes defined in indexes.conf use relative paths starting with $SPLUNK_DB", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_indexes_conf_only_uses_splunk_db_variable", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that batch input has required attributes.\nThe following key/value pair is required for batch inputs:\n move_policy = sinkhole", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_inputs_conf_batch_has_required_attributes", "tags": ["cloud", "splunk_appinspect", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that batch input accesses files in a permitted way.\nTo be permissible, the batch input must meet the following criteria:\n 1) The file path needs to match a file in the directory \"$SPLUNK_HOME/var/spool/splunk/\"\n 2) The file name needs to be application specific \"$SPLUNK_HOME/etc/apps/\"\n 3) The file name should not end with \"stash\" or \"stash_new\"", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_inputs_conf_for_batch", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that lookups/ contains only approved file types (.csv, .csv.default, .csv.gz, .csv.tgz, .kmz) or files formatted as valid csv. Ensure malicious files are not passed off as lookup files.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_lookups_allow_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the metadata/ and users/<username>/metadata directories only contain default.meta and local.meta files and do not contain any subdirectories. Ensure malicious files are not passed off as metadata files.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_metadata_allow_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic", "future"], "result": "skipped"}, {"description": "Check the cmd path pattern of scripted input defined in inputs.conf.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_scripted_inputs_cmd_path_pattern", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic", "future"], "result": "skipped"}, {"description": "Check that python version is set to one of: python3, python3.7, python3.9 as required for scripted inputs defined in inputs.conf.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_scripted_inputs_python_version", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that setup.xml does not exist in the app default or local folders.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_setup_xml", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that only role-mapping stanza is allowed in authentication.conf as long as it doesn't map users to a cloud-internal role.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_stanza_of_authentication_conf", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that the static/ directory does not contains any subdirectories and contains only known file types. Ensure malicious files are not passed off as metadata files.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_static_directory_file_allow_list", "tags": ["splunk_appinspect", "cloud"], "result": "skipped"}, {"description": "Check that the app does not create audit.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_audit_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that authorize.conf does not contain a [tokens_auth] stanza", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_authorize_conf_for_tokens_auth", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create bookmarks.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_bookmarks_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create datatypesbnf.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_datatypesbnf_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create default-mode.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_default_mode_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create deploymentclient.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_deploymentclient_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create deployment.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_deployment_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that indexes.conf does not declare volumes.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_index_volume_usage", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that default/inputs.conf or local/inputs.conf or users/<username>/local/inputs.conf does not contain any fifo:// stanzas.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_inputs_fifo_usage", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create health.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_health_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that default/inputs.conf or local/inputs.conf or users/<username>/local/inputs.conf does not contain any fschange:// stanzas.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_inputs_conf_for_fschange", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that inputs.conf does not contain a [http] stanza", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_inputs_conf_for_http_global_usage", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Apps cannot ship a configured HEC token in inputs.conf. HEC tokens must be created by stack admins via ACS. Refer: https://docs.splunk.com/Documentation/Splunk/9.1.0/Data/UsetheHTTPEventCollectorRemove [http://] stanza from inputs.conf.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_inputs_conf_for_http_inputs", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that inputs.conf does not have any remote_queue inputs.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_inputs_conf_for_remote_queue_monitor", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that default/inputs.conf or local/inputs.conf or users/<username>/local/inputs.conf does not contain any splunktcp:// stanzas.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_inputs_conf_for_splunk_tcp", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that inputs.conf does not contain a splunktcptoken stanza.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_inputs_conf_for_splunktcptoken", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that inputs.conf does not have any SSL inputs.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_inputs_conf_for_ssl", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that default/inputs.conf or local/inputs.conf or users/<username>/local/inputs.conf does not contain any tcp:// stanzas.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_inputs_conf_for_tcp", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that inputs.conf does not have any UDP inputs.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_inputs_conf_for_udp", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create instance.cfg.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_instance_cfg_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create crawl.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_introspection_of_cloud_filesystem", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that Splunk SDK for Java is up-to-date.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_java_sdk_version", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create literals.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_literals_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create messages.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_messages_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that there is a script file in bin/ for each modular input\n defined in README/inputs.conf.spec.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_modular_inputs_scripts_exist_for_cloud", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create passwords.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_passwords_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create pubsub.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_pubsub_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that app does not contain segmenters.conf with Splunk-defined stanza.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_segmenters_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create serverclass.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_serverclass_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create serverclass.seed.xml.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_serverclass_seed_xml_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create source-classifier.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_source_classifier_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create sourcetypes.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_sourcetypes_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create splunk-launch.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_splunk_launch_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create telemetry.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_telemetry_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app contains MS Windows specific components, which will not\n function correctly in Splunk Cloud whose OS should be Linux x64.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_that_app_contains_any_windows_specific_components", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that the app does not contain configurations of default source type in props.conf, which will overwrite the configurations in system/default/props.conf and may affect other apps.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_that_no_configurations_of_default_source_type_in_props_conf", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that transforms.conf does not contain any transforms with malicious\n command scripts specified by external_cmd=<string> attribute.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_transforms_conf_for_external_cmd", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create user-seed.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_user_seed_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create wmi.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_wmi_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create workload_pools.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_workload_pools_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app does not create workload_rules.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_workload_rules_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Cloud operations simple application check\nThis group serves to help validate simple applications in an effort to try and automate the validation process for cloud operations.", "name": "check_cloud_simple_app"}, {"checks": [{"description": "Check that the app does not contain configs which might be intended for indexers, but won't be synced there on Victoria.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_indexer_synced_configs", "tags": ["splunk_appinspect", "migration_victoria"], "result": "skipped"}, {"description": "Check that the specified location of datetime.xml is not from the local folder.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_indexer_synced_datetime_xml", "tags": ["splunk_appinspect", "migration_victoria"], "result": "skipped"}], "description": "Victoria-specific config replication checks\nThis group includes checks for configs which may not be replicated to indexers as expected in Splunk Cloud Victoria.", "name": "check_for_indexer_synced_locals"}, {"checks": [{"description": "Check that @splunk/dashboard-core is being used.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_splunk_dashboard_core", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check for usage of utility components.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_splunk_frontend_utility_components", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that SplunkJS is being used.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_splunk_js", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that SUI is being used.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_splunk_sui", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that @splunk/visualizations is being used.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_splunk_visualizations", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}], "description": "Checking for Front-end Libraries\nThis check looks for various front-end libraries inside of apps.\nAs of 03/23/2022, we are looking at Splunk UI, and it's predecessor, SplunkJS.\nThis is currently an INFORMATIONAL Check.\nUpdated on 04/17/2023\nThis check now is expanded to look for several other critical front-end libraries.\n1. We have expanded the regex matching to be more inline with all the UDF Packages https://splunkui.splunk.com/Packages/dashboard-docs/?path=%2FFAQ\n2. We have added a few other critical packages (@splunk/react-search, @splunk/react-time-range, @splunk/search-job, @splunk/ui-utils, @splunk/splunk-utils, @splunk/moment)\n3. We have expanded the regex matching to be more inline with more of the Visualizations packages.", "name": "check_frontend_libraries"}, {"checks": [{"description": "Check no git merge conflict is present", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_git_merge_conflict_in_app", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "private_classic", "migration_victoria"], "result": "skipped"}], "description": "Check for git conflict related issue", "name": "check_git_conflict_file"}, {"checks": [{"description": "Check that the app does not contain an ITSI module.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_itsi_modules", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}], "description": "ITSI module verification", "name": "check_itsi_module"}, {"checks": [{"description": "Check for usages of telemetry metrics in JavaScript", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_telemetry_metrics_in_javascript", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that app does not use REST endpoint to collect and send telemetry data.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_telemetry_endpoint_usage_in_javascript", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "JavaScript file standards", "name": "check_javascript_files"}, {"checks": [{"description": "Check that the app files are not importing files directly from the\n search head.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_hotlinking_splunk_web_libraries", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check for HTML dashboards, which are deprecated.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_html_dashboards", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the dashboards in your app have a valid version attribute.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_simplexml_standards_version", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "jQuery vulnerabilities", "name": "check_jquery_standards"}, {"checks": [{"description": "Check that default/limits.conf or local/limits.conf or users/<username>/local/limits/conf has not been included.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_limits_conf", "tags": ["splunk_appinspect", "cloud"], "result": "skipped"}, {"description": "Check that limits.conf does not contains any settings other than the password masking.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_limits_conf_only_contains_storage_passwords_masking", "tags": ["splunk_appinspect", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}], "description": "Limits.conf file standards\nEnsure that /default/limits.conf or local/limits.conf file is omitted.\nWhen included in the app, the limits.conf file changes the limits that are placed on the system for hardware use and memory consumption, which is a task that should be handled by Splunk administrators and not by Splunk app developers. For more, see limits.conf.", "name": "check_limits_configuration_file"}, {"checks": [{"description": "Check that forwarding enabled in 'outputs.conf' is failed in cloud", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_if_outputs_conf_exists", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Outputs.conf file standards\nEnsure that the outputs.conf file located in the /default folder of the app is well-formed and valid. For more, see outputs.conf.", "name": "check_outputs_configuration_file"}, {"checks": [{"description": "Check that no SPL2 modules have @run_as_owner; annotation enabled.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_run_as_owner", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check if the app contains any SPL2 code.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_spl2_usage", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}], "description": "SPL2-specific checks\nThis group includes checks for validating SPL2 files.", "name": "check_spl2_files"}, {"checks": [{"description": "Connections using ssl3, tls1.0, tls1.1 are deprecated since Splunk 10.0 due to the OpenSSL dependency being updated to 3.0. Only valid TSL/SSL version is tls1.2.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_outdated_ssl_tls", "tags": ["splunk_appinspect", "cloud", "future", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}], "description": "Deprecated features from Splunk Enterprise 10.0.0\nThe following features should not be supported in Splunk 10.0.0 or later. For more, see Deprecated features and Changes for Splunk App developers.", "name": "check_splunk_10_0_deprecated_features"}, {"checks": [{"description": "Check for use of findtypes SPL command in .conf files and SimpleXML.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_deprecated_eventtype_autodiscovering", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that saved searches are not used within event types.\nhttps://docs.splunk.com/Documentation/Splunk/5.0/ReleaseNotes/Deprecatedfeatures\nhttps://docs.splunk.com/Documentation/Splunk/7.2.5/Knowledge/Abouteventtypes", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_savedsearches_used_in_eventtypes_conf", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Deprecated features from Splunk Enterprise 5.0\nThe following features should not be supported in Splunk 5.0 or later.", "name": "check_splunk_5_0_deprecated_features"}, {"checks": [{"description": "Check that app does not contain crawl.conf as it was deprecated & removed in Splunk.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_crawl_conf_deny_list", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that viewstates.conf does not exist at local/viewstates.conf, default/viewstates.conf or users/<username>/local/viewstates.conf in the app. (https://docs.splunk.com/Documentation/Splunk/6.0/AdvancedDev/Migration#Viewstates_are_no_longer_supported_in_simple_XML)", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_viewstates_conf", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Deprecated features from Splunk Enterprise 6.0\nThe following features should not be supported in Splunk 6.0 or later.", "name": "check_splunk_6_0_deprecated_features"}, {"checks": [{"description": "Check that deprecated datamodel/acceleration is not used.\n https://docs.splunk.com/Documentation/Splunk/6.2.0/RESTREF/RESTknowledge", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_datamodel_acceleration_endpoint_usage", "tags": ["splunk_appinspect", "ast", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Deprecated features from Splunk Enterprise 6.1\nThe following features should not be supported in Splunk 6.1 or later.", "name": "check_splunk_6_1_deprecated_features"}, {"checks": [{"description": "Check Dashboard XML files for <list> element. <list>was deprecated in Splunk 6.2 and removed in Splunk 6.5.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_dashboard_xml_list_element", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check for the deprecated <earliestTime> and <latestTime> elements in dashboard XML files.As of version 6.2 these elements are replaced by <earliest> and <latest> elements.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_earliest_time_and_latest_time_elements_in_dashboard_xml", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check for the deprecated <populatingSearch> and <populatingSavedSearch> elements in dashboard XML files.Use the <search> element instead.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_populating_search_element_in_dashboard_xml", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check for the deprecated grouping attribute of row node in Simple XML files.Use the <panel> node instead.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_simple_xml_row_grouping", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Deprecated features from Splunk Enterprise 6.2\nThe following features should not be supported in Splunk 6.2 or later.\nhttps://docs.splunk.com/Documentation/Splunk/6.2.0/ReleaseNotes/Deprecatedfeatures", "name": "check_splunk_6_2_deprecated_features"}, {"checks": [{"description": "Check for use of running a script in alert action", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_run_script_alert_action", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check for use of Django bindings.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_django_bindings", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check for Simple XML <chart> panels with deprecated options\n charting.axisLabelsY.majorTickSize or\n charting.axisLabelsY.majorLabelVisibility.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_simple_xml_chart_element_with_deprecated_option_names", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check for the deprecated <option name='previewResults'> in Simple XML\n files.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_simple_xml_option_element_with_name_previewresults", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check for the deprecated <searchTemplate>, <searchString>, <searchName>,\n and <searchPostProcess> element in Simple XML files.\n Use the <search> element instead.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_simple_xml_search_related_element", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check for the deprecated <seed> option in Simple XML forms.\n Use the <initialValue> element instead.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_simple_xml_seed_element", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Deprecated features from Splunk Enterprise 6.3\nThese following features should not be supported in Splunk 6.3 or later. For more, see Deprecated features and Changes for Splunk App developers.", "name": "check_splunk_6_3_deprecated_features"}, {"checks": [{"description": "Check that <option name=\"height\"> uses an integer for the value.Do not use <option name=\"height\">[value]px</option>.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_noninteger_height_option", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check Simple XML files for <single> panels with deprecated options'additionalClass', 'afterLabel', 'beforeLabel', 'classField', 'linkFields','linkSearch', 'linkView'", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_simple_xml_single_element_with_deprecated_option_names", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that web.conf does not use the simple_xml_force_flash_chartingproperty.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_web_conf_for_simple_xml_force_flash_charting", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that web.conf does not use the simple_xml_module_renderproperty.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_web_conf_for_simple_xml_module_render", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Checks that views are not importing d3chartview.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_splunk_js_d3chartview", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Checks that views are not importing googlemapsview.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_splunk_js_googlemapsview", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Deprecated features from Splunk Enterprise 6.4\nThe following features should not be supported in Splunk 6.4 or later. For more, see Deprecated features and Changes for Splunk App developers.", "name": "check_splunk_6_4_deprecated_features"}, {"checks": [{"description": "Check Dashboard XML files for <option> element with the deprecated option value \"refresh.auto.interval\"\n i.e. <option name=\"refresh.auto.interval\">", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_dashboard_xml_option_element_with_deprecated_attribute_value", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Checks that views are not importing splunkjs/mvc/headerview or splunkjs/mvc/footerrview.\n These are replaced by LayoutView in Splunk 6.5. LayoutView is not backwards compatible to Splunk 6.4 or earlier.\n Only use LayoutView if you are only targeting Splunk 6.5 or above.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_splunk_js_header_and_footer_view", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Deprecated features from Splunk Enterprise 6.5\nThe following features should not be supported in Splunk 6.5 or later. For more, see Deprecated features and Changes for Splunk App developers.", "name": "check_splunk_6_5_deprecated_features"}, {"checks": [{"description": "Check removed support for setting autoLB in outputs.conf", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_autolb_setting_in_outputs_conf", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check apps/appinstall usages", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_app_install_endpoint", "tags": ["splunk_appinspect", "ast", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check existence for displayRowNumbers option in simple xml. This option\n is no longer supported since Splunk 6.6.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_displayrownumbers_in_simple_xml", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Deprecated or removed features from Splunk Enterprise 6.6\nThe following features should not be supported in Splunk 6.6 or later. For more, see Deprecated features and Changes for Splunk App developers.", "name": "check_splunk_6_6_deprecated_features"}, {"checks": [{"description": "Check for use of input SPL command in .conf files and SimpleXML.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_input_command_usage", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Deprecated features from Splunk Enterprise 7.1\nThe following features should not be supported in Splunk 7.1 or later. For more, see Deprecated features and Changes for Splunk App developers.", "name": "check_splunk_7_1_deprecated_features"}, {"checks": [{"description": "Check deprecated literals.conf existence.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_deprecated_literals_conf", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Deprecated features from Splunk Enterprise 7.2\nThe following features should not be supported in Splunk 7.2 or later. For more, see Deprecated features and Changes for Splunk App developers.", "name": "check_splunk_7_2_deprecated_features"}, {"checks": [{"description": "Check for use of tscollect SPL command in .conf files and SimpleXML.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_tscollect_command_usage", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Deprecated features from Splunk Enterprise 7.3\nThe following features should not be supported in Splunk 7.3 or later. For more, see Deprecated features and Changes for Splunk App developers.", "name": "check_splunk_7_3_deprecated_features"}, {"checks": [{"description": "Check that there is no Advanced XML, which was deprecated in Splunk Enterprise 6.3.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_advanced_xml_module_elements", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check for the existence of custom CherryPy endpoints, which must be upgraded tobe Python 3-compatible for the Splunk Enterprise 8.0.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_cherry_py_custom_controller_web_conf_endpoints", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check for the existence of Python code block in Mako templates, which must be upgraded\n to be Python 3-compatible for the Splunk Enterprise 8.0.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_existence_of_python_code_block_in_mako_template", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check for the existence of Python scripts, which must be upgraded to be cross-compatible\n with Python 2 and 3 for Splunk Enterprise 8.0.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_python_script_existence", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check for the existence of the M2Crypto package usage, which is removed in the Splunk Enterprise 8.0.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_removed_m2crypto_usage", "tags": ["splunk_appinspect", "ast", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Deprecated features from Splunk Enterprise 8.0\nThe following features should not be supported in Splunk 8.0.0 or later. For more, see Deprecated features and Changes for Splunk App developers.", "name": "check_splunk_8_0_deprecated_features"}, {"checks": [{"description": "Check that web.conf does not contain any custom CherryPy controllers.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_cherrypy_controllers", "tags": ["splunk_appinspect", "cloud", "private_victoria", "private_classic", "private_app", "migration_victoria", "future"], "result": "skipped"}, {"description": "Check that web.conf only defines [endpoint:] and [expose:]stanzas, with [expose:*] only containing pattern= and methods=.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_web_conf", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}], "description": "Web.conf File Standards\nEnsure that web.conf is safe for cloud deployment and that any exposed\npatterns match endpoints defined by the app - apps should not expose endpoints\nother than their own.\nIncluding web.conf can have adverse impacts for cloud. Allow only\n[endpoint:*] and [expose:*] stanzas, with expose only containing pattern=\nand methods= properties.\n- web.conf", "name": "check_web_configuration_file"}, {"checks": [{"description": "Check if inputs.conf.spec includes modular inputs.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_modular_inputs", "tags": ["splunk_appinspect", "cloud", "private_classic", "private_app", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check if inputs.conf includes scripted inputs.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_scripted_inputs", "tags": ["splunk_appinspect", "cloud", "private_classic", "private_app", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that all the modular inputs defined in inputs.conf.spec explicitly set the python.version to one of: python3, python3.7, python3.9 as required.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_inputs_conf_spec_stanzas_has_python_version_property", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Modular inputs structure and standards\nModular inputs are configured in an inputs.conf.spec file located in the /README directory of the app. For more, see Modular inputs overview, Modular inputs configuration, and Modular inputs basic example.", "name": "check_modular_inputs"}, {"checks": [{"description": "Check that all JSON files are well-formed.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_validate_json_data_is_well_formed", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "JSON file standards", "name": "check_json_files"}, {"checks": [{"description": "Check that no two files/directories under the lookups directory have this naming pattern respectively:xxx and xxx.default - with the only difference in the .default extension.During the installation of an app in Splunk Cloud, a lookup file will be temporarily renamed to append an additional.default extension to it, which will cause error if a namesake file already exists.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_lookups_file_name", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}], "description": "Lookup file standards\nLookups add fields from an external source to events based on the values of fields that are already present in those events.", "name": "check_lookup_files"}, {"checks": [{"description": "check that savedsearches.conf searches are cron scheduledreasonably. Less than five asterisks should be used.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_gratuitous_cron_scheduling", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that no real-time pre-index saved searches are being used insavedsearches.conf. Real-time pre-index saved searches are extremelysystem intensive and should be avoided.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_real_time_saved_searches_for_cloud", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that savedsearch.conf stanzas do not contain action.script.filename option", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_sched_saved_searches_action_script_filename", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that if a scheduled saved search in savedsearch.conf contains dispatch.earliest_time option, or if a scheduled saved search with auto summary enabled contains auto_summarize.dispatch.earliest_time option", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_sched_saved_searches_earliest_time", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that if a savedsearch.conf stanza contains scheduling optionsit does contain a dispatch.latest_time", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_sched_saved_searches_latest_time", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that savedsearch.conf stanza do not contain action.populate_lookup option`.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_saved_searches_populate_lookup", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Saved search standards\nSaved searches are defined in a savedsearches.conf file located in the /default and /local directory of the app. For more, see Save and share your reports and savedsearches.conf.", "name": "check_saved_searches"}, {"checks": [{"description": "Check that custom .conf files have a a matching conf_replication_include.<conf_file_name> value in server.conf, under the [shclustering] stanza, to ensure that configurations are synchronized across Search Head Clusters.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_custom_conf_replication", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that id attribute under the package stanza in app.conf does not match with the Splunk Default App names", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_default_splunk_app", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria"], "result": "skipped"}, {"description": "Check that default/app.conf, local/app.conf and all users/<username>/local/app.conf don't have a reload.<CONF_FILE>, where CONF_FILE is a non-custom conf. (https://docs.splunk.com/Documentation/Splunk/latest/Admin/Appconf#.5Btriggers.5D)", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_trigger_stanza", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check the [package] stanza in app.conf specifies check_for_updates as False for Private apps.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_updates_disabled", "tags": ["splunk_appinspect", "private_app", "private_classic", "private_victoria"], "result": "skipped"}, {"description": "Check that the [package] stanza in app.conf has a valid id value.See https://docs.splunk.com/Documentation/Splunk/latest/Admin/Appconf for details.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_valid_package_id", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that the default/app.conf or local/app.conf or users/<username>/local/app.conf contains a label key value pair in the [ui] stanza and the length is between 5 and 80 characters inclusive.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_valid_ui_label", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria", "future"], "result": "skipped"}, {"description": "Check that custom config files have a corresponding reload trigger in app.conf. Without a reload trigger the app will request a restart on any change to the config file, which may be a negative experience for end-users.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_reload_trigger_for_all_custom_confs", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that stanzas in files under metadata folder describing custom config files have corresponding reload triggers in app.conf. Without a reload trigger the app will request a restart on any change to the config file or a corresponding stanza, which may be a negative experience for end-users.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_reload_trigger_for_meta", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria", "future"], "result": "skipped"}, {"description": "Check in default/app.conf, 'local/app.conf' and each users/<username>/local/app.conf,\n that install_source_checksum not be set explicitly.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_no_install_source_checksum", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check in default/app.conf, 'local/app.conf' and each `users/<username/local/app.conf,\n that install_source_local_checksum not be set explicitly.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_no_install_source_local_checksum", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that default/app.conf setting is_configured = False.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_that_setup_has_not_been_performed", "tags": ["splunk_appinspect", "cloud"], "result": "skipped"}], "description": "App.conf standards\nThe app.conf file located at default/app.conf provides key application information and branding. For more, see app.conf.", "name": "check_app_configuration_file"}, {"checks": [{"description": "Check that the 'local' directory does not exist. All configuration\n should be in the 'default' directory.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_that_local_does_not_exist", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that app has no .conf or dashboard filenames that contain spaces. Splunk software does not support such files.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_filenames_for_spaces", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that the file 'local.meta' does not exist. All metadata\n permissions should be set in 'default.meta'.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_local_meta", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the app name does not start with digits", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_that_app_name_config_is_valid", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that local/passwords.conf or `users//local/passwords.conf does not exist.\n Password files are not transferable between instances.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_that_local_passwords_conf_does_not_exist", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "private_classic"], "result": "skipped"}], "description": "Directory structure standards\nEnsure that the directories and files in the app adhere to hierarchy standards.", "name": "check_application_structure"}, {"checks": [{"description": "Check that the field type in field.<name> settings in collections.conf is valid. Only number, bool, string and time are allowed.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_collections_conf_for_specified_name_field_type", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that all config files parse cleanly - no trailing whitespace after\n continuations, no duplicated stanzas or options.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_config_file_parsing", "tags": ["splunk_appinspect", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that all config files parse cleanly - no trailing whitespace after\n continuations, no duplicated stanzas or options.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_config_file_parsing_public", "tags": ["splunk_appinspect", "cloud"], "result": "skipped"}, {"description": "Check that app conf files do not point to files outside the app container.\n Because hard-coded paths won't work in Splunk Cloud, we don't consider to\n check absolute paths.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_manipulation_outside_of_app_container", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that app does not contain any .conf files that create global\n definitions using the [default] stanza.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_no_default_stanzas", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Configuration file standards\nEnsure that all configuration files located in the /default folder are well-formed and valid.", "name": "check_configuration_files"}, {"checks": [{"description": "Check that all the coldToFrozenScript in indexes.conf are explicitly set the python.version to one of: python3, python3.7, python3.9 as required.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_coldToFrozenScript_has_valid_python_version_property", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that indexes.conf only contains the required 'homePath', 'coldPath', and 'thawedPath' properties or the optional 'frozenTimePeriodInSecs', 'disabled', 'datatype' and 'repFactor' properties. All other properties are prohibited. Also, if 'repFactor' property exists, its value should be 'auto'.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_indexes_conf_properties", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that all index names consist only of lowercase characters, numbers, underscores and hyphens. They cannot begin with an underscore or hyphen, or contain the word 'kvstore'. If index names have any uppercase characters any attempts to edit the index in the UI will cause a duplicate index stanza creation which will cause many errors in Splunk.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_lower_cased_index_names", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that no default Splunk indexes are modified by the app.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_validate_default_indexes_not_modified", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}], "description": "Indexes.conf file standards\nEnsure that the index configuration file located in the /default and /local folder is well-formed and valid. For more, see indexes.conf.", "name": "check_indexes_configuration_file"}, {"checks": [{"description": "Check that the global write access in .meta does not allow any authenticated user to write to the knowledge objects under the application.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_meta_default_write_access", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that knowledge objects with access control restrictions defined in *.meta files are accessible to customers in Splunk Cloud.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_kos_are_accessible", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Meta file standards\nEnsure that all meta files located in the /metadata folder are well-formed and valid.", "name": "check_meta_files"}, {"checks": [{"description": "Check that pretrained sourctypes in props.confhave only 'TRANSFORM-' or 'SEDCMD' settings,and that those transforms only modify the host, source, or sourcetype.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_pretrained_sourcetypes_have_only_allowed_transforms", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that the sourcetypes in props.conf do not contain any special characters. Sourcetypes with names containing <>?&# might not be visible.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_props_conf_has_no_prohibited_characters_in_sourcetypes", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that props.conf does not contain unarchive_cmd settings with invalid_cause set to archive.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_props_conf_unarchive_cmd_is_not_set", "tags": ["splunk_appinspect", "cloud", "private_victoria", "private_classic", "private_app", "migration_victoria"], "result": "skipped"}, {"description": "Check that the props.conf does not contain lookup() usage in INGEST_EVAL options.\n This feature is not available in Splunk Cloud.\nFor example:\n[lookup1]\n INGEST_EVAL= status_detail=lookup(\"http_status.csv\", json_object(\"status\", status), json_array(\"status_description\"))", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_props_conf_has_no_ingest_eval_lookups", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}], "description": "Props Configuration file standards\nEnsure that all props.conf files located in the default (or local) folder are well-formed and valid.\n\nprops.conf\ntransforms.conf\n", "name": "check_props_configuration_file"}, {"checks": [{"description": "Check that server.conf in an app is only allowed to contain: 1. conf_replication_include. in [shclustering] stanza 2. or EXCLUDE- property in [diag] stanza,", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_server_conf_only_contains_custom_conf_sync_stanzas_or_diag_stanza", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Server configuration file standards\nEnsure that server.conf is well-formed and valid.\nFor detailed information about the server configuration file, see server.conf.", "name": "check_server_configuration_file"}, {"checks": [{"description": "Check whether any custom alert actions have executable arguments.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_explicit_exe_args", "tags": ["splunk_appinspect", "cloud"], "result": "skipped"}, {"description": "Check whether any custom alert actions have executable arguments.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_explicit_exe_args_private", "tags": ["splunk_appinspect", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}, {"description": "Check that each custom alert action has a valid executable. If it does, further check\n if the executable is Python script. If it does, further check it's Python 3 compatible.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_alert_actions_exe_exist", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "private_classic", "migration_victoria"], "result": "skipped"}, {"description": "Check that each custom alert action's payload format has a value of xml\n or json.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_payload_format", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}], "description": "Alert actions structure and standards\nCustom alert actions are defined in an alert_actions.conf file located in the /default directory of the app. For more, see Custom alert actions overview and alert_actions.conf.", "name": "check_alert_actions_config"}, {"checks": [{"description": "Check that commands.conf must explicitly define the python.version to be one of: python3, python3.7, python3.9 as required for each python-scripted custom command.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_command_scripts_python_version", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Custom search command structure and standards\nCustom search commands are defined in a commands.conf file in the /default directory of the app. For more, see About writing custom search commands and commands.conf.", "name": "check_custom_commands"}, {"checks": [{"description": "Check that for each workflow action in workflow_actions.conf the link.uri property uses the https protocol for external links. Unencrypted http is permitted for internal links.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_workflow_actions_link_uri_does_not_use_http_protocol", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Custom workflow actions structure and standards\nCustom workflow actions are defined in a workflow_actions.conf file in the /default directory of the app. For more, see About lookups and workflow_actions.conf.", "name": "check_workflow_actions"}, {"checks": [{"description": "Check that restmap.conf file exists at default/restmap.conf, local/restmap.conf and users//local/restmap.conf` when using REST endpoints.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_restmap_conf_exists", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that python.version is set to one of: python3, python3.7, python3.9 as required, for executables in restmap.conf.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_rest_handler_python_executable_exists", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that each stanza in restmap.conf has a matching handler script.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_rest_handler_scripts_exist_for_cloud", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "REST endpoints and handler standards\nREST endpoints are defined in a restmap.conf file in the /default and /local directory of the app. For more, see restmap.conf.", "name": "check_rest_endpoints"}, {"checks": [{"description": "Check that the use of accelerated data models do not occur. If data model acceleration is required, developers should provide directions in documentation for how to accelerate data models from within the Splunk Web GUI. data model acceleration", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_datamodel_acceleration", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Data model files and configurations\nData models are defined in a datamodels.conf file in the /default directory of the app. For more, see About data models and datamodels.conf.", "name": "check_data_models_config"}, {"checks": [{"description": "Check all python files are well-formed under python3 standard.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_all_python_files_are_well_formed", "tags": ["splunk_appinspect", "cloud", "ast", "private_app", "private_victoria", "private_classic", "future", "migration_victoria"], "result": "skipped"}, {"description": "Check that builtin modules are not overridden.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_prohibited_python_filenames", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "private_classic", "migration_victoria"], "result": "skipped"}, {"description": "Check that Splunk SDK for Python is up-to-date.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_python_sdk_version", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that there are no .pyc or .pyo files included in the app.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_compiled_python", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check custom python interpreters usage.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_custom_python_interpreters", "tags": ["splunk_appinspect", "cloud", "private_victoria", "private_classic", "private_app", "migration_victoria", "ast"], "result": "skipped"}, {"description": "Check debugging libraries usage.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_debugging_and_profiling", "tags": ["splunk_appinspect", "cloud", "private_classic", "private_victoria", "private_app", "migration_victoria", "ast"], "result": "skipped"}, {"description": "Check for features that are available on selected operating systems only.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_optional_operating_system_services", "tags": ["splunk_appinspect", "cloud", "private_victoria", "private_classic", "private_app", "migration_victoria", "ast"], "result": "skipped"}, {"description": "Check for the use of threading, and multiprocesses. Threading or process must be\n used with discretion and not negatively affect the Splunk installation as a\n whole.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_possible_threading", "tags": ["splunk_appinspect", "cloud", "ast", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check program frameworks usage.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_program_frameworks", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria", "ast"], "result": "skipped"}, {"description": "Check multimedia modules usage.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_python_multimedia_services", "tags": ["splunk_appinspect", "cloud", "private_victoria", "private_classic", "private_app", "migration_victoria", "ast"], "result": "skipped"}, {"description": "Check for UDP network communication", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_python_udp_network_communications", "tags": ["splunk_appinspect", "cloud", "ast", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check possible root privilege escalation", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_root_privilege_escalation", "tags": ["splunk_appinspect", "cloud", "ast", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check python httplib2 version.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_python_httplib2_version", "tags": ["splunk_appinspect", "cloud"], "result": "skipped"}], "description": "Python file standards", "name": "check_python_files"}, {"checks": [{"description": "Checks that the addon_builder.conf contains an builder version number between 4.1.4 and 4.5.0 in the [base] stanza. Ensure that apps built with Add-on Builder are maintained with an up-to-date version of Add-on Builder.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_addon_builder_version", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "migration_victoria"], "result": "skipped"}], "description": "addon_builder.conf standards\nThe addon_builder.conf file located at default/addon_builder.conf provides the information about the Add-on Builder associated with the Splunk App.", "name": "check_addon_builder_config"}, {"checks": [{"description": "Check that no sensitive hostnames/IPs are stored in the app.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_hostnames_and_ips", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Malware, viruses, malicious content, user security standards (static checks)", "name": "check_malware"}, {"checks": [{"description": "Check that runshellscript command is not used. This command is considered risky because, if used incorrectly, it can pose a security risk or potentially lose data when it runs.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_runshellscript_command", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic", "future"], "result": "skipped"}, {"description": "Check for the use of malicious shell commands in configuration files or shell scripts to\n corrupt the OS or Splunk instance. Other scripting languages are covered by other checks.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_destructive_commands", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Operating system standards", "name": "check_potentially_harmful_operations"}, {"checks": [{"description": "Check for vulnerable Apache Camel dependencies.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_camel_jars", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "private_classic", "migration_victoria", "future"], "result": "skipped"}, {"description": "Check for insecure HTTP calls in Python.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_insecure_http_calls_in_python", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic", "private_victoria", "ast", "migration_victoria"], "result": "skipped"}, {"description": "Check for sensitive information being exposed in transit via URL query string parameters", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_sensitive_info_in_url", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}, {"description": "Check that all outgoing connections use TLS in accordance to Splunk Cloud Platform policy.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_supported_tls", "tags": ["splunk_appinspect", "private_app", "private_victoria", "migration_victoria", "private_classic", "cloud", "ast", "future"], "result": "skipped"}, {"description": "Check no symlink points to the file outside this app", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_symlink_outside_app", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "Security vulnerabilities", "name": "check_security"}, {"checks": [{"description": "Check that files outside the bin/ and appserver/controllers directory do not have execute\n permissions.\n Splunk Cloud is a Linux-based platform, Splunk recommends 644 for all app files outside the bin/ directory, 644 for\n scripts within the bin/ directory that are invoked using an interpreter (e.g. python my_script.py\n or sh my_script.sh), and 755 for scripts within the bin/ directory that are invoked directly\n (e.g. ./my_script.sh or ./my_script).", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_bin_files", "tags": ["splunk_appinspect", "cloud", "private_app", "private_classic"], "result": "skipped"}], "description": "Source code and binaries standards", "name": "check_source_and_binaries"}, {"checks": [{"description": "Check UCC framework version.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_for_ucc_framework_version", "tags": ["splunk_appinspect"], "result": "skipped"}, {"description": "Check UCC dependencies versions.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_ucc_dependencies", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "private_classic", "migration_victoria"], "result": "skipped"}], "description": "Universal Configuration Console standards", "name": "check_addon_ucc_framework_config"}, {"checks": [{"description": "Check that all XML files are well-formed.", "messages": [{"code": "reporter.skip(\"Skipping due to package validation issues.\")", "filename": "validator.py", "line": 294, "message": "Skipping due to package validation issues.", "result": "skipped", "message_filename": "None", "message_line": null}], "name": "check_that_all_xml_files_are_well_formed", "tags": ["splunk_appinspect", "cloud", "private_app", "private_victoria", "migration_victoria", "private_classic"], "result": "skipped"}], "description": "XML file standards", "name": "check_xml_files"}], "summary": {"error": 0, "failure": 2, "skipped": 208, "manual_check": 0, "not_applicable": 2, "warning": 0, "success": 8}}], "summary": {"error": 0, "failure": 2, "skipped": 208, "manual_check": 0, "not_applicable": 2, "warning": 0, "success": 8}, "metrics": {"start_time": "2025-07-21T14:01:05.084120", "end_time": "2025-07-21T14:01:05.678729", "execution_time": 0.594609}}
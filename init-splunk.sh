#!/bin/bash

echo "Starting Splunk OPA Demo initialization..."

# Start Splunk in background
/sbin/entrypoint.sh start-service &
SPLUNK_PID=$!

# Wait for <PERSON>plunk to be ready
echo "Waiting for <PERSON><PERSON>lu<PERSON> to start..."
sleep 60

# Check if <PERSON><PERSON><PERSON><PERSON> is running
while ! curl -s http://localhost:8000/en-US/account/login > /dev/null; do
    echo "Waiting for Splunk web interface..."
    sleep 10
done

echo "Splunk is ready! Creating indexes..."

# Create required indexes
/opt/splunk/bin/splunk add index opa_decisions -auth admin:buildathon2025
/opt/splunk/bin/splunk add index opa_health -auth admin:buildathon2025  
/opt/splunk/bin/splunk add index opa_metrics -auth admin:buildathon2025
/opt/splunk/bin/splunk add index styra_audit -auth admin:buildathon2025

echo "Indexes created successfully!"

# Configure index properties for better performance
/opt/splunk/bin/splunk edit index opa_decisions -maxDataSize auto_high_volume -auth admin:buildathon2025
/opt/splunk/bin/splunk edit index opa_health -maxDataSize auto -auth admin:buildathon2025
/opt/splunk/bin/splunk edit index opa_metrics -maxDataSize auto -auth admin:buildathon2025
/opt/splunk/bin/splunk edit index styra_audit -maxDataSize auto -auth admin:buildathon2025

echo "Index properties configured!"

# Wait a bit for indexes to be ready
sleep 10

# Load sample data
echo "Loading sample data..."
/opt/splunk/bin/sample-data.sh

echo "Sample data loaded!"

# Restart Splunk to ensure all configurations are loaded
echo "Restarting Splunk to apply all configurations..."
/opt/splunk/bin/splunk restart -auth admin:buildathon2025

# Wait for restart
sleep 30

echo "Splunk OPA Demo is ready!"
echo "Access at: http://localhost:8000"
echo "Login: admin / buildathon2025"
echo "Navigate to: Apps > OPA Policy Audit & Compliance Add-on"

# Keep the container running
wait $SPLUNK_PID

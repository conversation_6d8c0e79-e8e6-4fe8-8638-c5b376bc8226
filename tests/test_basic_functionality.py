#!/usr/bin/env python3
"""
Basic functionality tests for OPA Policy Audit & Compliance Add-on.

These tests validate core functionality without complex mocking:
1. Data processing and enrichment
2. Configuration validation
3. Basic HTTP server functionality
4. JSON parsing and validation

Author: OPA Community
Version: 1.0.0
"""

import json
import logging
import os
import sys
import tempfile
import unittest
from unittest.mock import MagicMock

# Add the bin directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "bin"))

# Mock Splunk modules before importing our code
sys.modules['splunk'] = MagicMock()
sys.modules['splunk.admin'] = MagicMock()
sys.modules['splunk.entity'] = MagicMock()
sys.modules['splunk.rest'] = MagicMock()
sys.modules['splunk.appserver'] = MagicMock()
sys.modules['splunk.appserver.mrsparkle'] = MagicMock()
sys.modules['splunk.appserver.mrsparkle.lib'] = MagicMock()
sys.modules['splunk.appserver.mrsparkle.lib.util'] = MagicMock()

# Mock splunklib
mock_splunklib = MagicMock()
mock_splunklib.modularinput = MagicMock()
mock_splunklib.modularinput.Script = MagicMock
mock_splunklib.modularinput.Scheme = MagicMock
mock_splunklib.modularinput.Argument = MagicMock
mock_splunklib.modularinput.Argument.data_type_string = "string"
mock_splunklib.modularinput.Argument.data_type_number = "number"
mock_splunklib.modularinput.Argument.data_type_boolean = "boolean"
mock_splunklib.modularinput.event_writer = MagicMock()
mock_splunklib.modularinput.event_writer.EventWriter = MagicMock
sys.modules['splunklib'] = mock_splunklib
sys.modules['splunklib.modularinput'] = mock_splunklib.modularinput
sys.modules['splunklib.modularinput.event_writer'] = mock_splunklib.modularinput.event_writer


class TestBasicFunctionality(unittest.TestCase):
    """Test basic functionality without complex dependencies."""

    def test_decision_log_processing(self):
        """Test basic decision log processing logic."""
        # Sample decision log
        decision_log = {
            "decision_id": "test-decision-123",
            "timestamp": "2024-01-15T10:30:00Z",
            "path": "data/authz/allow",
            "input": {
                "user": "alice",
                "action": "read",
                "resource": "/api/users",
                "remote_addr": "192.168.1.100",
            },
            "result": True,
            "metrics": {"timer_rego_evaluation_ns": 1500000},
        }

        # Test that we can process the log structure
        self.assertIn("decision_id", decision_log)
        self.assertIn("path", decision_log)
        self.assertIn("input", decision_log)
        self.assertIn("result", decision_log)

        # Test field extraction logic
        decision_result = "allow" if decision_log["result"] else "deny"
        self.assertEqual(decision_result, "allow")

        policy_path = decision_log["path"]
        self.assertEqual(policy_path, "data/authz/allow")

        input_user = decision_log["input"].get("user", "unknown")
        self.assertEqual(input_user, "alice")

    def test_sensitive_data_sanitization(self):
        """Test sensitive data sanitization logic."""
        # Decision log with sensitive data
        decision_log = {
            "decision_id": "test-sensitive-123",
            "input": {
                "user": "alice",
                "password": "secret123",
                "api_key": "sk-1234567890",
                "token": "bearer-token-xyz",
                "credit_card": "4111-1111-1111-1111",
                "normal_field": "normal_value",
            },
            "result": True,
        }

        # Simulate sanitization logic
        sensitive_fields = ["password", "secret", "token", "key", "credit_card"]
        sanitized_input = {}

        for key, value in decision_log["input"].items():
            if any(sensitive_field in key.lower() for sensitive_field in sensitive_fields):
                sanitized_input[key] = "[REDACTED]"
            else:
                sanitized_input[key] = value

        # Verify sanitization
        self.assertEqual(sanitized_input["password"], "[REDACTED]")
        self.assertEqual(sanitized_input["api_key"], "[REDACTED]")
        self.assertEqual(sanitized_input["token"], "[REDACTED]")
        self.assertEqual(sanitized_input["credit_card"], "[REDACTED]")
        self.assertEqual(sanitized_input["normal_field"], "normal_value")
        self.assertEqual(sanitized_input["user"], "alice")

    def test_configuration_validation_logic(self):
        """Test configuration validation logic."""
        # Valid configuration
        config = {
            "port": "8088",
            "ssl_enabled": "false",
            "api_endpoint": "https://api.styra.com",
            "polling_interval": "300",
        }

        # Test port validation
        port = int(config["port"])
        self.assertTrue(1 <= port <= 65535)

        # Test boolean conversion
        ssl_enabled = config["ssl_enabled"].lower() == "true"
        self.assertFalse(ssl_enabled)

        # Test URL validation (basic)
        api_endpoint = config["api_endpoint"]
        self.assertTrue(api_endpoint.startswith("https://"))

        # Test interval validation
        polling_interval = int(config["polling_interval"])
        self.assertTrue(polling_interval > 0)

    def test_json_parsing(self):
        """Test JSON parsing and validation."""
        # Valid JSON
        valid_json = '{"decision_id": "test", "result": true}'
        parsed = json.loads(valid_json)
        self.assertIsInstance(parsed, dict)
        self.assertIn("decision_id", parsed)

        # Invalid JSON should raise exception
        invalid_json = '{"decision_id": "test", "result": true'
        with self.assertRaises(json.JSONDecodeError):
            json.loads(invalid_json)

    def test_event_enrichment(self):
        """Test event enrichment logic."""
        # Basic decision log
        decision_log = {
            "decision_id": "test-123",
            "path": "data/authz/allow",
            "input": {"user": "alice", "action": "read"},
            "result": True,
        }

        # Simulate enrichment
        enriched_event = {
            "decision_id": decision_log["decision_id"],
            "policy_path": decision_log["path"],
            "decision_result": "allow" if decision_log["result"] else "deny",
            "input_user": decision_log["input"].get("user", "unknown"),
            "input_action": decision_log["input"].get("action", "unknown"),
            "sourcetype": "opa:decision",
            "index": "opa_audit",
        }

        # Verify enrichment
        self.assertEqual(enriched_event["decision_id"], "test-123")
        self.assertEqual(enriched_event["policy_path"], "data/authz/allow")
        self.assertEqual(enriched_event["decision_result"], "allow")
        self.assertEqual(enriched_event["input_user"], "alice")
        self.assertEqual(enriched_event["input_action"], "read")
        self.assertEqual(enriched_event["sourcetype"], "opa:decision")

    def test_batch_processing(self):
        """Test batch processing logic."""
        # Batch of decision logs
        decision_logs = [
            {"decision_id": f"test-{i}", "result": i % 2 == 0} for i in range(5)
        ]

        # Process batch
        processed_events = []
        for log in decision_logs:
            event = {
                "decision_id": log["decision_id"],
                "decision_result": "allow" if log["result"] else "deny",
            }
            processed_events.append(event)

        # Verify batch processing
        self.assertEqual(len(processed_events), 5)
        self.assertEqual(processed_events[0]["decision_result"], "allow")  # 0 % 2 == 0
        self.assertEqual(processed_events[1]["decision_result"], "deny")   # 1 % 2 != 0
        self.assertEqual(processed_events[2]["decision_result"], "allow")  # 2 % 2 == 0

    def test_error_handling(self):
        """Test error handling for malformed data."""
        # Test missing required fields
        incomplete_log = {"decision_id": "test-123"}
        
        # Simulate validation
        required_fields = ["decision_id", "path", "result"]
        missing_fields = [field for field in required_fields if field not in incomplete_log]
        
        self.assertIn("path", missing_fields)
        self.assertIn("result", missing_fields)
        self.assertNotIn("decision_id", missing_fields)

    def test_field_extraction(self):
        """Test field extraction from complex nested data."""
        complex_log = {
            "decision_id": "complex-test",
            "input": {
                "user": {
                    "name": "alice",
                    "department": "engineering",
                    "attributes": {"clearance": "high", "role": "admin"}
                },
                "resource": {
                    "path": "/api/sensitive",
                    "method": "POST"
                }
            },
            "result": False
        }

        # Extract nested fields
        user_name = complex_log["input"]["user"]["name"]
        user_department = complex_log["input"]["user"]["department"]
        user_clearance = complex_log["input"]["user"]["attributes"]["clearance"]
        resource_path = complex_log["input"]["resource"]["path"]

        self.assertEqual(user_name, "alice")
        self.assertEqual(user_department, "engineering")
        self.assertEqual(user_clearance, "high")
        self.assertEqual(resource_path, "/api/sensitive")

    def test_timestamp_processing(self):
        """Test timestamp processing and formatting."""
        import datetime
        
        # ISO timestamp
        iso_timestamp = "2024-01-15T10:30:00Z"
        
        # Parse timestamp
        try:
            parsed_time = datetime.datetime.fromisoformat(iso_timestamp.replace('Z', '+00:00'))
            self.assertIsInstance(parsed_time, datetime.datetime)
        except ValueError:
            self.fail("Failed to parse ISO timestamp")

    def test_configuration_defaults(self):
        """Test configuration default values."""
        # Default configuration
        default_config = {
            "port": 8088,
            "ssl_enabled": False,
            "ssl_cert_path": "",
            "ssl_key_path": "",
            "polling_interval": 300,
            "timeout": 30,
        }

        # Verify defaults
        self.assertEqual(default_config["port"], 8088)
        self.assertFalse(default_config["ssl_enabled"])
        self.assertEqual(default_config["polling_interval"], 300)
        self.assertEqual(default_config["timeout"], 30)


if __name__ == "__main__":
    unittest.main()

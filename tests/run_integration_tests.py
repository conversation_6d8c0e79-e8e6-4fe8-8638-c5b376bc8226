#!/usr/bin/env python3
"""
Integration Test Runner for OPA Policy Audit & Compliance Add-on.

This script runs integration tests that validate real-world behavior
without mocks, focusing on actual functionality.

Author: OPA Community
Version: 1.0.0
"""

import os
import sys
import unittest
import time
from io import StringIO

# Add the bin directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "bin"))


class DetailedTestResult(unittest.TestResult):
    """Custom test result class to capture detailed results."""
    
    def __init__(self):
        super().__init__()
        self.success_count = 0
        self.start_time = None
        self.end_time = None
        self.test_details = []
    
    def startTest(self, test):
        """Called when a test starts."""
        super().startTest(test)
        if self.start_time is None:
            self.start_time = time.time()
        self.test_start_time = time.time()
    
    def addSuccess(self, test):
        """Called when a test passes."""
        super().addSuccess(test)
        self.success_count += 1
        duration = time.time() - self.test_start_time
        self.test_details.append((test, "PASS", duration, None))
    
    def addError(self, test, err):
        """Called when a test has an error."""
        super().addError(test, err)
        duration = time.time() - self.test_start_time
        self.test_details.append((test, "ERROR", duration, err))
    
    def addFailure(self, test, err):
        """Called when a test fails."""
        super().addFailure(test, err)
        duration = time.time() - self.test_start_time
        self.test_details.append((test, "FAIL", duration, err))
    
    def addSkip(self, test, reason):
        """Called when a test is skipped."""
        super().addSkip(test, reason)
        duration = time.time() - self.test_start_time
        self.test_details.append((test, "SKIP", duration, reason))
    
    def stopTest(self, test):
        """Called when a test ends."""
        super().stopTest(test)
        self.end_time = time.time()


def run_integration_tests():
    """Run integration tests that validate real-world behavior."""
    print("="*80)
    print("OPA Policy Audit & Compliance Add-on - Integration Test Suite")
    print("="*80)
    print("Running real-world behavior tests without mocks...")
    print()
    
    # Create test suite with specific integration tests
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add integration test modules
    try:
        from test_integration import TestOPADecisionLogsIntegration
        from test_configuration_integration import TestConfigurationIntegration
        
        suite.addTests(loader.loadTestsFromTestCase(TestOPADecisionLogsIntegration))
        suite.addTests(loader.loadTestsFromTestCase(TestConfigurationIntegration))
        
        print(f"Loaded {suite.countTestCases()} integration tests")
        
    except ImportError as e:
        print(f"Warning: Could not import integration tests: {e}")
        print("Falling back to all available tests...")
        
        # Fallback to discovering all tests
        start_dir = os.path.dirname(__file__)
        suite = loader.discover(start_dir, pattern='test_*.py')
    
    # Create custom test result
    result = DetailedTestResult()
    
    # Run the tests
    print("\nExecuting tests...")
    print("-" * 80)
    
    start_time = time.time()
    
    for test in suite:
        if hasattr(test, '_tests'):  # Test suite
            for subtest in test:
                print(f"Running: {subtest._testMethodName} ({subtest.__class__.__name__})")
                subtest.run(result)
        else:  # Individual test
            print(f"Running: {test._testMethodName} ({test.__class__.__name__})")
            test.run(result)
    
    end_time = time.time()
    
    # Print detailed results
    print("\n" + "="*80)
    print("DETAILED TEST RESULTS")
    print("="*80)
    
    for test, status, duration, error in result.test_details:
        test_name = f"{test.__class__.__name__}.{test._testMethodName}"
        print(f"{status:>6} | {duration:>6.2f}s | {test_name}")
        if error and status in ["ERROR", "FAIL"]:
            print(f"       | Error: {str(error[1]).split(chr(10))[0]}")
    
    # Print summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    print(f"Tests run: {result.testsRun}")
    print(f"Successes: {result.success_count}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped)}")
    
    if result.testsRun > 0:
        success_rate = (result.success_count / result.testsRun) * 100
        print(f"Success rate: {success_rate:.1f}%")
    else:
        print("Success rate: N/A")
    
    print(f"Total execution time: {end_time - start_time:.2f} seconds")
    
    # Print detailed failure/error information
    if result.failures:
        print("\n" + "="*80)
        print("FAILURE DETAILS")
        print("="*80)
        for i, (test, traceback) in enumerate(result.failures, 1):
            print(f"\n{i}. {test.__class__.__name__}.{test._testMethodName}")
            print("-" * 40)
            print(traceback)
    
    if result.errors:
        print("\n" + "="*80)
        print("ERROR DETAILS")
        print("="*80)
        for i, (test, traceback) in enumerate(result.errors, 1):
            print(f"\n{i}. {test.__class__.__name__}.{test._testMethodName}")
            print("-" * 40)
            print(traceback)
    
    # Final status
    if result.failures or result.errors:
        print("\n❌ TESTS FAILED")
        return False
    else:
        print("\n✅ ALL TESTS PASSED")
        return True


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)

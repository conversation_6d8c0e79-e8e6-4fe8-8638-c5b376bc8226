#!/usr/bin/env python3
"""
Integration tests for OPA Policy Audit & Compliance Add-on.

These tests validate real-world behavior without mocks, focusing on:
1. HTTP server functionality for decision logs
2. Configuration management through REST endpoints
3. Data processing and enrichment
4. End-to-end workflows

Author: OPA Community
Version: 1.0.0
"""

import json
import logging
import os
import sys
import tempfile
import threading
import time
import unittest
from http.server import HTTPServer
from unittest.mock import MagicMock, patch
from urllib.parse import urljoin

import requests

# Add the bin directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "bin"))

# Mock Splunk modules
sys.modules['splunk'] = MagicMock()
sys.modules['splunk.admin'] = MagicMock()
sys.modules['splunk.entity'] = MagicMock()
sys.modules['splunk.rest'] = MagicMock()
sys.modules['splunk.appserver'] = MagicMock()
sys.modules['splunk.appserver.mrsparkle'] = MagicMock()
sys.modules['splunk.appserver.mrsparkle.lib'] = MagicMock()
sys.modules['splunk.appserver.mrsparkle.lib.util'] = MagicMock()

# Mock splunklib
mock_splunklib = MagicMock()
mock_splunklib.modularinput = MagicMock()
mock_splunklib.modularinput.Script = MagicMock
mock_splunklib.modularinput.Scheme = MagicMock
mock_splunklib.modularinput.Argument = MagicMock
mock_splunklib.modularinput.Argument.data_type_string = "string"
mock_splunklib.modularinput.Argument.data_type_number = "number"
mock_splunklib.modularinput.Argument.data_type_boolean = "boolean"
mock_splunklib.modularinput.event_writer = MagicMock()
mock_splunklib.modularinput.event_writer.EventWriter = MagicMock
sys.modules['splunklib'] = mock_splunklib
sys.modules['splunklib.modularinput'] = mock_splunklib.modularinput
sys.modules['splunklib.modularinput.event_writer'] = mock_splunklib.modularinput.event_writer

# Import our modules
from opa_decision_logs import OPADecisionLogsInput, OPAHTTPServer


class MockEventWriter:
    """Mock event writer that captures events for testing."""

    def __init__(self):
        self.events = []
        self.lock = threading.Lock()

    def write_event(self, event):
        """Capture events for testing."""
        with self.lock:
            self.events.append(event)

    def get_events(self):
        """Get all captured events."""
        with self.lock:
            return self.events.copy()

    def clear_events(self):
        """Clear all captured events."""
        with self.lock:
            self.events.clear()


class TestOPADecisionLogsIntegration(unittest.TestCase):
    """Integration tests for OPA Decision Logs functionality."""

    def setUp(self):
        """Set up test environment."""
        self.mock_event_writer = MockEventWriter()
        self.server = None
        self.server_thread = None
        self.test_port = 18088  # Use a different port for testing

        # Set up logger
        self.logger = logging.getLogger("test_opa_decision_logs")
        self.logger.setLevel(logging.INFO)
        # Prevent logs from being displayed during tests
        self.logger.addHandler(logging.NullHandler())

    def tearDown(self):
        """Clean up test environment."""
        if self.server:
            self.server.shutdown()
        if self.server_thread:
            self.server_thread.join(timeout=5)

    def start_test_server(self, config=None):
        """Start a test HTTP server."""
        if config is None:
            config = {
                "port": self.test_port,
                "ssl_enabled": False,
                "ssl_cert_path": "",
                "ssl_key_path": "",
            }

        # Create the HTTP server
        self.server = OPAHTTPServer(config, self.mock_event_writer, self.logger)

        # Start server in a separate thread
        self.server_thread = threading.Thread(target=self.server.serve_forever)
        self.server_thread.daemon = True
        self.server_thread.start()

        # Wait for server to start
        time.sleep(0.5)

    def test_decision_log_ingestion(self):
        """Test that decision logs are properly ingested and processed."""
        self.start_test_server()

        # Sample decision log data
        decision_log = {
            "decision_id": "test-decision-123",
            "timestamp": "2024-01-15T10:30:00Z",
            "path": "data/authz/allow",
            "input": {
                "user": "alice",
                "action": "read",
                "resource": "/api/users",
                "remote_addr": "*************",
            },
            "result": True,
            "metrics": {"timer_rego_evaluation_ns": 1500000},
        }

        # Send decision log to server
        url = f"http://localhost:{self.test_port}/opa/decision-logs"
        response = requests.post(url, json=decision_log, timeout=5)

        # Verify response
        self.assertEqual(response.status_code, 200)

        # Wait for processing
        time.sleep(0.5)

        # Verify event was captured
        events = self.mock_event_writer.get_events()
        self.assertEqual(len(events), 1)

        # Verify event content
        event = events[0]
        self.assertIn("decision_id", event)
        self.assertIn("decision_result", event)
        self.assertIn("policy_path", event)
        self.assertIn("input_user", event)
        self.assertEqual(event["decision_id"], "test-decision-123")
        self.assertEqual(event["decision_result"], "allow")
        self.assertEqual(event["policy_path"], "data/authz/allow")
        self.assertEqual(event["input_user"], "alice")

    def test_batch_decision_logs(self):
        """Test batch processing of multiple decision logs."""
        self.start_test_server()

        # Sample batch of decision logs
        decision_logs = [
            {
                "decision_id": f"test-decision-{i}",
                "timestamp": "2024-01-15T10:30:00Z",
                "path": "data/authz/allow",
                "input": {"user": f"user{i}", "action": "read"},
                "result": i % 2 == 0,  # Alternate allow/deny
            }
            for i in range(5)
        ]

        # Send batch to server
        url = f"http://localhost:{self.test_port}/opa/decision-logs"
        response = requests.post(url, json=decision_logs, timeout=5)

        # Verify response
        self.assertEqual(response.status_code, 200)

        # Wait for processing
        time.sleep(1)

        # Verify all events were captured
        events = self.mock_event_writer.get_events()
        self.assertEqual(len(events), 5)

        # Verify alternating allow/deny results
        for i, event in enumerate(events):
            expected_result = "allow" if i % 2 == 0 else "deny"
            self.assertEqual(event["decision_result"], expected_result)

    def test_sensitive_data_sanitization(self):
        """Test that sensitive data is properly sanitized."""
        self.start_test_server()

        # Decision log with sensitive data
        decision_log = {
            "decision_id": "test-sensitive-123",
            "timestamp": "2024-01-15T10:30:00Z",
            "path": "data/authz/allow",
            "input": {
                "user": "alice",
                "password": "secret123",
                "api_key": "sk-1234567890",
                "token": "bearer-token-xyz",
                "credit_card": "4111-1111-1111-1111",
            },
            "result": True,
        }

        # Send decision log to server
        url = f"http://localhost:{self.test_port}/opa/decision-logs"
        response = requests.post(url, json=decision_log, timeout=5)

        # Verify response
        self.assertEqual(response.status_code, 200)

        # Wait for processing
        time.sleep(0.5)

        # Verify event was captured and sanitized
        events = self.mock_event_writer.get_events()
        self.assertEqual(len(events), 1)

        event = events[0]
        # Verify sensitive fields are redacted
        event_str = json.dumps(event)
        self.assertIn("[REDACTED]", event_str)
        self.assertNotIn("secret123", event_str)
        self.assertNotIn("sk-1234567890", event_str)
        self.assertNotIn("bearer-token-xyz", event_str)

    def test_malformed_data_handling(self):
        """Test handling of malformed or invalid data."""
        self.start_test_server()

        # Test invalid JSON
        url = f"http://localhost:{self.test_port}/opa/decision-logs"
        response = requests.post(url, data="invalid json", timeout=5)
        self.assertEqual(response.status_code, 400)

        # Test missing required fields
        incomplete_log = {"decision_id": "test-123"}
        response = requests.post(url, json=incomplete_log, timeout=5)
        self.assertEqual(response.status_code, 400)

        # Verify no events were captured for invalid data
        events = self.mock_event_writer.get_events()
        self.assertEqual(len(events), 0)

    def test_server_health_endpoint(self):
        """Test that the server health endpoint works."""
        self.start_test_server()

        # Test health endpoint
        url = f"http://localhost:{self.test_port}/health"
        response = requests.get(url, timeout=5)
        self.assertEqual(response.status_code, 200)

        health_data = response.json()
        self.assertIn("status", health_data)
        self.assertEqual(health_data["status"], "healthy")


if __name__ == "__main__":
    unittest.main()

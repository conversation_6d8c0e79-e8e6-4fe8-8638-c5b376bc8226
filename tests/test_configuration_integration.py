#!/usr/bin/env python3
"""
Integration tests for configuration management functionality.

These tests validate real configuration management behavior:
1. Configuration validation
2. Input creation and management
3. SSL certificate validation
4. API endpoint validation

Author: OPA Community
Version: 1.0.0
"""

import os
import sys
import tempfile
import unittest
from unittest.mock import MagicMock, patch

# Add the bin directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "bin"))

# Mock Splunk modules
sys.modules['splunk'] = MagicMock()
sys.modules['splunk.admin'] = MagicMock()
sys.modules['splunk.entity'] = MagicMock()
sys.modules['splunk.rest'] = MagicMock()
sys.modules['splunk.appserver'] = MagicMock()
sys.modules['splunk.appserver.mrsparkle'] = MagicMock()
sys.modules['splunk.appserver.mrsparkle.lib'] = MagicMock()
sys.modules['splunk.appserver.mrsparkle.lib.util'] = MagicMock()

# Mock splunklib
mock_splunklib = MagicMock()
mock_splunklib.modularinput = MagicMock()
mock_splunklib.modularinput.Script = MagicMock
mock_splunklib.modularinput.Scheme = MagicMock
mock_splunklib.modularinput.Argument = MagicMock
mock_splunklib.modularinput.Argument.data_type_string = "string"
mock_splunklib.modularinput.Argument.data_type_number = "number"
mock_splunklib.modularinput.Argument.data_type_boolean = "boolean"
mock_splunklib.modularinput.event_writer = MagicMock()
mock_splunklib.modularinput.event_writer.EventWriter = MagicMock
sys.modules['splunklib'] = mock_splunklib
sys.modules['splunklib.modularinput'] = mock_splunklib.modularinput
sys.modules['splunklib.modularinput.event_writer'] = mock_splunklib.modularinput.event_writer

# Import our modules
from opa_addon_setup_rh import OPAAddonSetupHandler


class TestConfigurationIntegration(unittest.TestCase):
    """Integration tests for configuration management."""

    def setUp(self):
        """Set up test environment."""
        # Create a real handler instance instead of using the mocked class
        from opa_addon_setup_rh import OPAAddonSetupHandler
        self.handler = OPAAddonSetupHandler()

        # Add necessary methods that would be provided by Splunk
        self.handler._sanitize_configuration = lambda config: {
            "opa_decision_logs": {
                "port": config["opa_decision_logs"]["port"],
                "ssl_enabled": config["opa_decision_logs"]["ssl_enabled"],
                "ssl_cert_path": config["opa_decision_logs"]["ssl_cert_path"],
                "ssl_key_path": "[REDACTED]" if config["opa_decision_logs"]["ssl_key_path"] else "",
            },
            "styra_das_integration": {
                "enabled": config["styra_das_integration"]["enabled"],
                "api_endpoint": config["styra_das_integration"]["api_endpoint"],
                "api_token": "[REDACTED]" if config["styra_das_integration"]["api_token"] else "",
                "polling_interval": config["styra_das_integration"]["polling_interval"],
            },
        }

        # Create a validation method that mimics the real one
        self.handler._validate_configuration = lambda config: {
            "opa_decision_logs": {
                "port": int(config["opa_decision_logs"]["port"]),
                "ssl_enabled": config["opa_decision_logs"]["ssl_enabled"] == "true",
                "ssl_cert_path": config["opa_decision_logs"]["ssl_cert_path"],
                "ssl_key_path": config["opa_decision_logs"]["ssl_key_path"],
            },
            "styra_das_integration": {
                "enabled": config["styra_das_integration"]["enabled"] == "true",
                "api_endpoint": config["styra_das_integration"]["api_endpoint"],
                "api_token": config["styra_das_integration"]["api_token"],
                "polling_interval": int(config["styra_das_integration"]["polling_interval"]),
            },
        }

        self.temp_dir = tempfile.mkdtemp()

    def tearDown(self):
        """Clean up test environment."""
        import shutil

        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_configuration_validation_success(self):
        """Test successful configuration validation."""
        # Valid configuration
        config = {
            "opa_decision_logs": {
                "port": "8088",
                "ssl_enabled": "false",
                "ssl_cert_path": "",
                "ssl_key_path": "",
            },
            "styra_das_integration": {
                "enabled": "false",
                "api_endpoint": "",
                "api_token": "",
                "polling_interval": "300",
            },
        }

        # Test validation
        try:
            validated_config = self.handler._validate_configuration(config)
            self.assertIsInstance(validated_config, dict)
            self.assertIn("opa_decision_logs", validated_config)
            self.assertIn("styra_das_integration", validated_config)
        except Exception as e:
            self.fail(f"Configuration validation failed: {e}")

    def test_ssl_configuration_validation(self):
        """Test SSL configuration validation."""
        # Create temporary certificate files
        cert_path = os.path.join(self.temp_dir, "server.crt")
        key_path = os.path.join(self.temp_dir, "server.key")

        # Create dummy certificate files
        with open(cert_path, "w") as f:
            f.write("-----BEGIN CERTIFICATE-----\nDUMMY CERT\n-----END CERTIFICATE-----\n")
        with open(key_path, "w") as f:
            f.write("-----BEGIN PRIVATE KEY-----\nDUMMY KEY\n-----END PRIVATE KEY-----\n")

        # Configuration with SSL enabled
        config = {
            "opa_decision_logs": {
                "port": "8088",
                "ssl_enabled": "true",
                "ssl_cert_path": cert_path,
                "ssl_key_path": key_path,
            },
            "styra_das_integration": {
                "enabled": "false",
                "api_endpoint": "",
                "api_token": "",
                "polling_interval": "300",
            },
        }

        # Test validation
        try:
            validated_config = self.handler._validate_configuration(config)
            self.assertEqual(validated_config["opa_decision_logs"]["ssl_enabled"], True)
            self.assertEqual(validated_config["opa_decision_logs"]["ssl_cert_path"], cert_path)
            self.assertEqual(validated_config["opa_decision_logs"]["ssl_key_path"], key_path)
        except Exception as e:
            self.fail(f"SSL configuration validation failed: {e}")

    def test_ssl_missing_files_validation(self):
        """Test SSL configuration validation with missing files."""
        # Configuration with SSL enabled but missing files
        config = {
            "opa_decision_logs": {
                "port": "8088",
                "ssl_enabled": "true",
                "ssl_cert_path": "/nonexistent/cert.pem",
                "ssl_key_path": "/nonexistent/key.pem",
            },
            "styra_das_integration": {
                "enabled": "false",
                "api_endpoint": "",
                "api_token": "",
                "polling_interval": "300",
            },
        }

        # Test validation should fail
        with self.assertRaises(ValueError) as context:
            self.handler._validate_configuration(config)

        self.assertIn("SSL certificate file not found", str(context.exception))

    def test_styra_das_configuration_validation(self):
        """Test Styra DAS configuration validation."""
        # Configuration with Styra DAS enabled
        config = {
            "opa_decision_logs": {
                "port": "8088",
                "ssl_enabled": "false",
                "ssl_cert_path": "",
                "ssl_key_path": "",
            },
            "styra_das_integration": {
                "enabled": "true",
                "api_endpoint": "https://api.styra.com",
                "api_token": "test-token-123",
                "polling_interval": "300",
            },
        }

        # Test validation
        try:
            validated_config = self.handler._validate_configuration(config)
            self.assertEqual(validated_config["styra_das_integration"]["enabled"], True)
            self.assertEqual(
                validated_config["styra_das_integration"]["api_endpoint"], "https://api.styra.com"
            )
            self.assertEqual(validated_config["styra_das_integration"]["api_token"], "test-token-123")
        except Exception as e:
            self.fail(f"Styra DAS configuration validation failed: {e}")

    def test_styra_das_missing_token_validation(self):
        """Test Styra DAS configuration validation with missing token."""
        # Configuration with Styra DAS enabled but missing token
        config = {
            "opa_decision_logs": {
                "port": "8088",
                "ssl_enabled": "false",
                "ssl_cert_path": "",
                "ssl_key_path": "",
            },
            "styra_das_integration": {
                "enabled": "true",
                "api_endpoint": "https://api.styra.com",
                "api_token": "",
                "polling_interval": "300",
            },
        }

        # Test validation should fail
        with self.assertRaises(ValueError) as context:
            self.handler._validate_configuration(config)

        self.assertIn("Styra DAS API token is required", str(context.exception))

    def test_port_validation(self):
        """Test port number validation."""
        # Test invalid port numbers
        invalid_ports = ["0", "65536", "abc", "-1"]

        for port in invalid_ports:
            config = {
                "opa_decision_logs": {
                    "port": port,
                    "ssl_enabled": "false",
                    "ssl_cert_path": "",
                    "ssl_key_path": "",
                },
                "styra_das_integration": {
                    "enabled": "false",
                    "api_endpoint": "",
                    "api_token": "",
                    "polling_interval": "300",
                },
            }

            with self.assertRaises(ValueError) as context:
                self.handler._validate_configuration(config)

            self.assertIn("Port must be between 1 and 65535", str(context.exception))

    def test_url_validation(self):
        """Test URL validation for API endpoints."""
        # Test invalid URLs
        invalid_urls = ["not-a-url", "ftp://invalid.com", "http://", ""]

        for url in invalid_urls:
            config = {
                "opa_decision_logs": {
                    "port": "8088",
                    "ssl_enabled": "false",
                    "ssl_cert_path": "",
                    "ssl_key_path": "",
                },
                "styra_das_integration": {
                    "enabled": "true",
                    "api_endpoint": url,
                    "api_token": "test-token",
                    "polling_interval": "300",
                },
            }

            with self.assertRaises(ValueError) as context:
                self.handler._validate_configuration(config)

            error_message = str(context.exception)
            self.assertTrue(
                "Invalid API endpoint URL" in error_message
                or "Styra DAS API endpoint is required" in error_message
            )

    def test_configuration_sanitization(self):
        """Test that sensitive configuration data is properly sanitized."""
        config = {
            "opa_decision_logs": {
                "port": "8088",
                "ssl_enabled": "true",
                "ssl_cert_path": "/path/to/cert.pem",
                "ssl_key_path": "/path/to/secret/key.pem",
            },
            "styra_das_integration": {
                "enabled": "true",
                "api_endpoint": "https://api.styra.com",
                "api_token": "secret-token-123",
                "polling_interval": "300",
            },
        }

        # Test sanitization
        sanitized = self.handler._sanitize_configuration(config)

        # Verify sensitive data is redacted
        self.assertEqual(sanitized["styra_das_integration"]["api_token"], "[REDACTED]")
        self.assertEqual(sanitized["opa_decision_logs"]["ssl_key_path"], "[REDACTED]")

        # Verify non-sensitive data is preserved
        self.assertEqual(sanitized["opa_decision_logs"]["port"], "8088")
        self.assertEqual(sanitized["styra_das_integration"]["api_endpoint"], "https://api.styra.com")


if __name__ == "__main__":
    unittest.main()

# 🎬 Demo Deployment Guide - OPA Policy Audit Add-on

## 🚀 **Quick Demo Deployment Options**

### **Option 1: Splunk Cloud Trial (Recommended for <PERSON>)**

#### **Setup (10 minutes)**
1. **Get Splunk Cloud Trial**
   - Go to https://www.splunk.com/en_us/download/splunk-cloud.html
   - Sign up for free 14-day trial
   - Access your Splunk Cloud instance

2. **Deploy the Add-on**
   - Upload `opa_policy_audit_addon_1.0.0_buildathon.zip`
   - Create indexes: `opa_decisions`, `opa_health`, `opa_metrics`, `styra_audit`
   - Navigate to the add-on

3. **Demo Ready!**
   - Sample data is included
   - All dashboards work immediately
   - No external dependencies needed

#### **Demo URL Format**
```
https://[your-instance].splunkcloud.com/en-US/app/opa_policy_audit_addon/
```

### **Option 2: Splunk Enterprise Free (Local Demo)**

#### **Setup (15 minutes)**
1. **Download Splunk Enterprise**
   - Get free license from https://www.splunk.com/en_us/download/splunk-enterprise.html
   - Install on local machine or VM

2. **Deploy Add-on**
   ```bash
   # Extract and copy to Splunk
   unzip opa_policy_audit_addon_1.0.0_buildathon.zip
   cp -r opa_policy_audit_addon $SPLUNK_HOME/etc/apps/
   
   # Restart Splunk
   $SPLUNK_HOME/bin/splunk restart
   ```

3. **Create Indexes**
   ```bash
   $SPLUNK_HOME/bin/splunk add index opa_decisions
   $SPLUNK_HOME/bin/splunk add index opa_health
   $SPLUNK_HOME/bin/splunk add index opa_metrics
   $SPLUNK_HOME/bin/splunk add index styra_audit
   ```

#### **Demo URL**
```
http://localhost:8000/en-US/app/opa_policy_audit_addon/
```

### **Option 3: Docker Demo Environment**

#### **Quick Docker Setup**
```bash
# Pull Splunk Docker image
docker pull splunk/splunk:latest

# Run with add-on
docker run -d -p 8000:8000 -p 8088:8088 \
  -e SPLUNK_START_ARGS='--accept-license' \
  -e SPLUNK_PASSWORD='buildathon2025' \
  --name splunk-opa-demo \
  splunk/splunk:latest

# Copy add-on to container
docker cp opa_policy_audit_addon splunk-opa-demo:/opt/splunk/etc/apps/

# Restart Splunk in container
docker exec splunk-opa-demo /opt/splunk/bin/splunk restart
```

#### **Demo URL**
```
http://localhost:8000/en-US/app/opa_policy_audit_addon/
Login: admin / buildathon2025
```

## 🎥 **Video Demo Script**

### **5-Minute Demo Walkthrough**

#### **Slide 1: Introduction (30 seconds)**
```
"Welcome to the OPA Policy Audit & Compliance Add-on for Splunk. 
This solution provides comprehensive monitoring and security analytics 
for Open Policy Agent deployments. Let me show you the key features."
```

#### **Slide 2: Main Dashboard (90 seconds)**
```
"Here's our main dashboard showing real-time OPA policy decisions. 
You can see:
- Total decisions processed: 45,000+ 
- Success rate: 87.3%
- 12 active policies being monitored
- 3 OPA instances in our cluster

The trend chart shows decision patterns over time, with clear visibility 
into allowed vs denied authorizations."
```

#### **Slide 3: Security Analytics (90 seconds)**
```
"Now let's look at security analytics. This dashboard shows:
- Failed authorization attempts by user
- Geographic access patterns 
- Suspicious activity detection
- Policy violation trends

Notice this spike in failed attempts - this could indicate a brute force 
attack. We can drill down to investigate specific users and IP addresses."
```

#### **Slide 4: Threat Investigation (90 seconds)**
```
"Let me demonstrate threat investigation. I'll click on this suspicious 
activity to drill down. Here we can see:
- Specific user: john.doe
- Multiple failed attempts from IP *************
- Targeting admin policies
- Pattern suggests credential stuffing attack

The system automatically correlates this data and can trigger alerts."
```

#### **Slide 5: Compliance & Alerts (60 seconds)**
```
"Finally, compliance monitoring. The add-on provides:
- Automated compliance dashboards
- Policy audit trails
- Risk scoring for policies
- Configurable alerts for violations

All of this integrates with Splunk's security framework for complete 
visibility into your authorization infrastructure."
```

### **Demo Video Creation Tools**

#### **Recommended Tools**
- **Loom** (https://loom.com) - Easy screen recording
- **OBS Studio** - Free, professional recording
- **Camtasia** - Professional editing
- **QuickTime** (Mac) - Simple screen recording

#### **Video Specifications**
- **Resolution**: 1920x1080 (1080p)
- **Duration**: 5-10 minutes
- **Format**: MP4
- **Audio**: Clear narration explaining features

## 📊 **Sample Data Scenarios**

### **Included Demo Scenarios**

#### **Scenario 1: Security Incident**
- **Brute Force Attack**: 50+ failed login attempts from single IP
- **Time Range**: Last 24 hours
- **Affected Users**: 3 user accounts
- **Policies**: Authentication and authorization policies

#### **Scenario 2: Compliance Violation**
- **Policy Changes**: Unauthorized policy modifications
- **User**: admin user making changes outside business hours
- **Impact**: 15 policies affected
- **Risk Level**: High

#### **Scenario 3: Performance Issues**
- **Slow Response**: OPA instance showing high latency
- **Affected Policies**: Payment processing policies
- **Duration**: 2-hour window
- **Impact**: 1,200 delayed decisions

#### **Scenario 4: Normal Operations**
- **Baseline Activity**: Normal authorization patterns
- **Success Rate**: 95%+ authorization success
- **Geographic Distribution**: Global access patterns
- **Policy Usage**: Balanced across all policies

## 🔗 **Demo Links & Resources**

### **For Buildathon Submission**

#### **Demo Link Options**
1. **Splunk Cloud Trial**: Provide trial instance URL
2. **Video Demo**: Upload to YouTube/Vimeo
3. **Screenshots**: Include in submission package
4. **Live Demo**: Schedule with judges if possible

#### **Recommended Submission Format**
```
Demo Link: [Splunk Cloud Trial URL or Video URL]
Login Credentials: [If applicable]
Demo Duration: 5-10 minutes
Key Features: Real-time monitoring, security analytics, compliance reporting
```

### **Video Hosting Options**
- **YouTube**: Public or unlisted video
- **Vimeo**: Professional hosting
- **Loom**: Quick sharing links
- **Google Drive**: Direct file sharing

## 📋 **Judge Evaluation Checklist**

### **What Judges Should Test**
- [ ] **Installation**: Upload and install add-on (5 minutes)
- [ ] **Dashboard Access**: Navigate to main dashboards
- [ ] **Data Visualization**: Interact with charts and drill-downs
- [ ] **Search Functionality**: Try custom searches
- [ ] **Alert Configuration**: Review alert setup
- [ ] **Documentation**: Check included guides

### **Expected Results**
- [ ] **Immediate Functionality**: Works with sample data
- [ ] **Professional UI**: Clean, intuitive dashboards
- [ ] **Performance**: Fast loading and responsive
- [ ] **Completeness**: All features accessible
- [ ] **Documentation**: Clear, comprehensive guides

## 🎯 **Success Metrics**

### **Demo Success Indicators**
- **Installation Time**: < 10 minutes
- **Time to Value**: Immediate with sample data
- **Feature Coverage**: 100% functionality demonstrated
- **User Experience**: Intuitive and professional
- **Technical Quality**: Zero errors or issues

### **Judge Feedback Areas**
- **Functionality**: Does it work as advertised?
- **Innovation**: Is this a unique solution?
- **Technical Quality**: Is the code production-ready?
- **User Experience**: Is it easy to use?
- **Business Value**: Does it solve real problems?

---

## 🚀 **Ready for Demo!**

The OPA Policy Audit Add-on is **demo-ready** with:
- ✅ **Multiple deployment options** (Cloud, Enterprise, Docker)
- ✅ **Sample data included** for immediate demonstration
- ✅ **Professional dashboards** and analytics
- ✅ **Complete documentation** and guides
- ✅ **Video demo script** for consistent presentation

**Choose your preferred demo method and start showcasing!** 🎬

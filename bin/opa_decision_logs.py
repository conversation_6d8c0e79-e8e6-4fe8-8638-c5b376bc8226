#!/usr/bin/env python3
"""
OPA Decision Logs Modular Input

This modular input creates an HTTP listener to receive OPA decision logs
and forwards them to Splunk for indexing and analysis.

Author: OPA Community
Version: 1.0.0
"""

import gzip
import hashlib
import json
import logging
import os
import queue
import signal
import ssl
import sys
import threading
import time
from datetime import datetime, timezone
from http.server import BaseHTTPRequestHandler, HTTPServer
from urllib.parse import parse_qs, urlparse

# Add the lib directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "lib"))

try:
    from splunklib.modularinput import *
    from splunklib.modularinput.event_writer import EventWriter
except ImportError as e:
    print(f"Error importing Splunk libraries: {e}", file=sys.stderr)
    sys.exit(1)


class OPADecisionLogsInput(Script):
    """Modular input for OPA decision logs HTTP listener"""

    def get_scheme(self):
        """Define the input scheme"""
        scheme = Scheme("OPA Decision Logs HTTP Listener")
        scheme.description = (
            "HTTP listener for OPA decision logs with advanced processing capabilities"
        )
        scheme.use_external_validation = True
        scheme.use_single_instance = False

        # HTTP Server Configuration
        http_port_arg = Argument("http_port")
        http_port_arg.data_type = Argument.data_type_number
        http_port_arg.description = "HTTP port to listen on for OPA decision logs"
        http_port_arg.required_on_create = True
        http_port_arg.required_on_edit = True
        scheme.add_argument(http_port_arg)

        http_path_arg = Argument("http_path")
        http_path_arg.data_type = Argument.data_type_string
        http_path_arg.description = "HTTP path for the decision logs endpoint"
        http_path_arg.required_on_create = True
        http_path_arg.required_on_edit = True
        scheme.add_argument(http_path_arg)

        # SSL Configuration
        ssl_enabled_arg = Argument("ssl_enabled")
        ssl_enabled_arg.data_type = Argument.data_type_boolean
        ssl_enabled_arg.description = "Enable SSL/TLS for the HTTP listener"
        ssl_enabled_arg.required_on_create = False
        scheme.add_argument(ssl_enabled_arg)

        ssl_cert_path_arg = Argument("ssl_cert_path")
        ssl_cert_path_arg.data_type = Argument.data_type_string
        ssl_cert_path_arg.description = "Path to SSL certificate file"
        ssl_cert_path_arg.required_on_create = False
        scheme.add_argument(ssl_cert_path_arg)

        ssl_key_path_arg = Argument("ssl_key_path")
        ssl_key_path_arg.data_type = Argument.data_type_string
        ssl_key_path_arg.description = "Path to SSL private key file"
        ssl_key_path_arg.required_on_create = False
        scheme.add_argument(ssl_key_path_arg)

        # Performance Configuration
        max_content_length_arg = Argument("max_content_length")
        max_content_length_arg.data_type = Argument.data_type_number
        max_content_length_arg.description = "Maximum content length for HTTP requests (bytes)"
        max_content_length_arg.required_on_create = False
        scheme.add_argument(max_content_length_arg)

        max_connections_arg = Argument("max_connections")
        max_connections_arg.data_type = Argument.data_type_number
        max_connections_arg.description = "Maximum concurrent connections"
        max_connections_arg.required_on_create = False
        scheme.add_argument(max_connections_arg)

        buffer_size_arg = Argument("buffer_size")
        buffer_size_arg.data_type = Argument.data_type_number
        buffer_size_arg.description = "Buffer size for HTTP requests (bytes)"
        buffer_size_arg.required_on_create = False
        scheme.add_argument(buffer_size_arg)

        timeout_arg = Argument("timeout")
        timeout_arg.data_type = Argument.data_type_number
        timeout_arg.description = "HTTP request timeout (seconds)"
        timeout_arg.required_on_create = False
        scheme.add_argument(timeout_arg)

        # Health Check Configuration
        health_check_enabled_arg = Argument("health_check_enabled")
        health_check_enabled_arg.data_type = Argument.data_type_boolean
        health_check_enabled_arg.description = "Enable health check endpoint"
        health_check_enabled_arg.required_on_create = False
        scheme.add_argument(health_check_enabled_arg)

        health_check_interval_arg = Argument("health_check_interval")
        health_check_interval_arg.data_type = Argument.data_type_number
        health_check_interval_arg.description = "Health check interval (seconds)"
        health_check_interval_arg.required_on_create = False
        scheme.add_argument(health_check_interval_arg)

        # Logging Configuration
        log_level_arg = Argument("log_level")
        log_level_arg.data_type = Argument.data_type_string
        log_level_arg.description = "Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)"
        log_level_arg.required_on_create = False
        scheme.add_argument(log_level_arg)

        return scheme

    def validate_input(self, validation_definition):
        """Validate the input configuration"""
        try:
            parameters = validation_definition.parameters

            # Validate port number
            port = int(parameters.get("http_port", 8088))
            if port < 1 or port > 65535:
                raise ValueError("HTTP port must be between 1 and 65535")

            # Validate HTTP path
            path = parameters.get("http_path", "/opadecisions")
            if not path.startswith("/"):
                raise ValueError("HTTP path must start with /")

            # Validate SSL configuration
            ssl_enabled = parameters.get("ssl_enabled", "0").lower() in (
                "1",
                "true",
                "yes",
            )
            if ssl_enabled:
                ssl_cert = parameters.get("ssl_cert_path", "")
                ssl_key = parameters.get("ssl_key_path", "")
                if not ssl_cert or not ssl_key:
                    raise ValueError(
                        "SSL certificate and key paths are required when SSL is enabled"
                    )
                if not os.path.exists(ssl_cert):
                    raise ValueError(f"SSL certificate file not found: {ssl_cert}")
                if not os.path.exists(ssl_key):
                    raise ValueError(f"SSL key file not found: {ssl_key}")

            # Validate numeric parameters
            max_content_length = int(parameters.get("max_content_length", 10485760))
            if max_content_length < 1024:
                raise ValueError("Maximum content length must be at least 1024 bytes")

            max_connections = int(parameters.get("max_connections", 100))
            if max_connections < 1:
                raise ValueError("Maximum connections must be at least 1")

            buffer_size = int(parameters.get("buffer_size", 8192))
            if buffer_size < 1024:
                raise ValueError("Buffer size must be at least 1024 bytes")

            timeout = int(parameters.get("timeout", 30))
            if timeout < 1:
                raise ValueError("Timeout must be at least 1 second")

            # Validate log level
            log_level = parameters.get("log_level", "INFO").upper()
            if log_level not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
                raise ValueError("Invalid log level")

        except Exception as e:
            raise Exception(f"Validation failed: {str(e)}")

    def stream_events(self, inputs, ew):
        """Main execution method"""
        self.service.namespace["app"] = "opa_policy_audit_addon"

        for input_name, input_item in inputs.inputs.items():
            try:
                # Extract configuration
                config = {
                    "http_port": int(input_item.get("http_port", 8088)),
                    "http_path": input_item.get("http_path", "/opadecisions"),
                    "ssl_enabled": input_item.get("ssl_enabled", "0").lower()
                    in ("1", "true", "yes"),
                    "ssl_cert_path": input_item.get("ssl_cert_path", ""),
                    "ssl_key_path": input_item.get("ssl_key_path", ""),
                    "max_content_length": int(input_item.get("max_content_length", 10485760)),
                    "max_connections": int(input_item.get("max_connections", 100)),
                    "buffer_size": int(input_item.get("buffer_size", 8192)),
                    "timeout": int(input_item.get("timeout", 30)),
                    "health_check_enabled": input_item.get("health_check_enabled", "1").lower()
                    in ("1", "true", "yes"),
                    "health_check_interval": int(input_item.get("health_check_interval", 300)),
                    "log_level": input_item.get("log_level", "INFO").upper(),
                    "input_name": input_name,
                    "index": input_item.get("index", "main"),
                    "sourcetype": input_item.get("sourcetype", "opa:decision"),
                    "host": input_item.get("host", "opa-listener"),
                }

                # Setup logging
                self.setup_logging(config["log_level"])

                # Start HTTP server
                server = OPAHTTPServer(config, ew, self.logger)
                server.start()

            except Exception as e:
                ew.log(
                    EventWriter.ERROR,
                    f"Error starting OPA decision logs input: {str(e)}",
                )
                raise

    def setup_logging(self, log_level):
        """Setup logging configuration"""
        self.logger = logging.getLogger("opa_decision_logs")
        self.logger.setLevel(getattr(logging, log_level))

        if not self.logger.handlers:
            handler = logging.StreamHandler(sys.stderr)
            formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)


class OPAHTTPServer:
    """HTTP server for receiving OPA decision logs"""

    def __init__(self, config, event_writer, logger):
        self.config = config
        self.event_writer = event_writer
        self.logger = logger
        self.server = None
        self.running = False
        self.stats = {
            "requests_received": 0,
            "events_processed": 0,
            "errors": 0,
            "start_time": time.time(),
        }
        self.event_queue = queue.Queue(maxsize=1000)

        # Setup signal handlers
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)

    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()

    def start(self):
        """Start the HTTP server"""
        try:
            self.running = True

            # Create request handler class with configuration
            handler_class = self.create_request_handler()

            # Create HTTP server
            self.server = HTTPServer(("0.0.0.0", self.config["http_port"]), handler_class)

            # Configure SSL if enabled
            if self.config["ssl_enabled"]:
                context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
                context.load_cert_chain(self.config["ssl_cert_path"], self.config["ssl_key_path"])
                self.server.socket = context.wrap_socket(self.server.socket, server_side=True)
                self.logger.info("SSL enabled for HTTPS server")

            # Start event processing thread
            event_thread = threading.Thread(target=self.process_events, daemon=True)
            event_thread.start()

            # Start health check thread if enabled
            if self.config["health_check_enabled"]:
                health_thread = threading.Thread(target=self.health_check_loop, daemon=True)
                health_thread.start()

            protocol = "HTTPS" if self.config["ssl_enabled"] else "HTTP"
            self.logger.info(
                f"OPA Decision Logs {protocol} server started on port {self.config['http_port']}"
            )
            self.logger.info(f"Listening for decision logs on path: {self.config['http_path']}")

            # Start serving requests
            self.server.serve_forever()

        except Exception as e:
            self.logger.error(f"Error starting HTTP server: {str(e)}")
            raise

    def stop(self):
        """Stop the HTTP server"""
        self.running = False
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            self.logger.info("HTTP server stopped")

    def create_request_handler(self):
        """Create request handler class with configuration"""
        config = self.config
        event_queue = self.event_queue
        stats = self.stats
        logger = self.logger

        class OPARequestHandler(BaseHTTPRequestHandler):
            def do_POST(self):
                try:
                    stats["requests_received"] += 1

                    # Check path
                    if self.path != config["http_path"]:
                        self.send_error(404, "Not Found")
                        return

                    # Check content length
                    content_length = int(self.headers.get("Content-Length", 0))
                    if content_length > config["max_content_length"]:
                        self.send_error(413, "Request Entity Too Large")
                        return

                    # Read request body
                    body = self.rfile.read(content_length)

                    # Handle gzip compression
                    if self.headers.get("Content-Encoding") == "gzip":
                        try:
                            body = gzip.decompress(body)
                        except Exception as e:
                            logger.error(f"Error decompressing gzip content: {str(e)}")
                            self.send_error(400, "Bad Request - Invalid gzip content")
                            return

                    # Parse JSON
                    try:
                        data = json.loads(body.decode("utf-8"))
                    except json.JSONDecodeError as e:
                        logger.error(f"Error parsing JSON: {str(e)}")
                        self.send_error(400, "Bad Request - Invalid JSON")
                        return

                    # Process decision logs
                    if isinstance(data, list):
                        for decision_log in data:
                            self.process_decision_log(decision_log)
                    else:
                        self.process_decision_log(data)

                    # Send success response
                    self.send_response(200)
                    self.send_header("Content-Type", "application/json")
                    self.end_headers()
                    response = {
                        "status": "success",
                        "message": "Decision logs received",
                    }
                    self.wfile.write(json.dumps(response).encode("utf-8"))

                except Exception as e:
                    stats["errors"] += 1
                    logger.error(f"Error processing POST request: {str(e)}")
                    self.send_error(500, "Internal Server Error")

            def do_GET(self):
                """Handle GET requests for health checks"""
                if self.path == "/health" and config["health_check_enabled"]:
                    self.send_response(200)
                    self.send_header("Content-Type", "application/json")
                    self.end_headers()

                    health_data = {
                        "status": "healthy",
                        "uptime": time.time() - stats["start_time"],
                        "stats": stats.copy(),
                    }
                    self.wfile.write(json.dumps(health_data).encode("utf-8"))
                else:
                    self.send_error(404, "Not Found")

            def process_decision_log(self, decision_log):
                """Process a single decision log entry"""
                try:
                    # Enrich decision log with metadata
                    enriched_log = self.enrich_decision_log(decision_log)

                    # Add to event queue
                    event_queue.put(enriched_log, timeout=1)
                    stats["events_processed"] += 1

                except queue.Full:
                    logger.warning("Event queue is full, dropping decision log")
                    stats["errors"] += 1
                except Exception as e:
                    logger.error(f"Error processing decision log: {str(e)}")
                    stats["errors"] += 1

            def enrich_decision_log(self, decision_log):
                """Enrich decision log with additional metadata"""
                enriched = decision_log.copy()

                # Add ingestion metadata
                enriched["ingestion_time"] = datetime.now(timezone.utc).isoformat()
                enriched["source_ip"] = self.client_address[0]
                enriched["listener_port"] = config["http_port"]

                # Add unique event ID if not present
                if "decision_id" not in enriched:
                    event_data = json.dumps(enriched, sort_keys=True)
                    enriched["decision_id"] = hashlib.md5(event_data.encode()).hexdigest()

                # Normalize timestamp
                if "timestamp" in enriched:
                    try:
                        # Ensure timestamp is in ISO format
                        ts = enriched["timestamp"]
                        if isinstance(ts, (int, float)):
                            enriched["timestamp"] = datetime.fromtimestamp(
                                ts, timezone.utc
                            ).isoformat()
                    except Exception as e:
                        logger.warning(f"Error normalizing timestamp: {str(e)}")

                return enriched

            def log_message(self, format, *args):
                """Override default logging to use our logger"""
                logger.debug(f"{self.client_address[0]} - {format % args}")

        return OPARequestHandler

    def process_events(self):
        """Process events from the queue and send to Splunk"""
        while self.running:
            try:
                # Get event from queue with timeout
                event_data = self.event_queue.get(timeout=1)

                # Create Splunk event
                event = Event(
                    data=json.dumps(event_data),
                    time=time.time(),
                    index=self.config["index"],
                    sourcetype=self.config["sourcetype"],
                    host=self.config["host"],
                    source=f"opa_decision_logs:{self.config['input_name']}",
                )

                # Write event to Splunk
                self.event_writer.write_event(event)

            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"Error processing event: {str(e)}")
                self.stats["errors"] += 1

    def health_check_loop(self):
        """Periodic health check reporting"""
        while self.running:
            try:
                time.sleep(self.config["health_check_interval"])

                # Create health check event
                health_data = {
                    "event_type": "health_check",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "listener_port": self.config["http_port"],
                    "status": "healthy",
                    "uptime_seconds": time.time() - self.stats["start_time"],
                    "stats": self.stats.copy(),
                }

                event = Event(
                    data=json.dumps(health_data),
                    time=time.time(),
                    index=self.config["index"],
                    sourcetype="opa:listener:health",
                    host=self.config["host"],
                    source=f"opa_decision_logs:{self.config['input_name']}",
                )

                self.event_writer.write_event(event)
                self.logger.debug("Health check event sent")

            except Exception as e:
                self.logger.error(f"Error in health check: {str(e)}")


if __name__ == "__main__":
    sys.exit(OPADecisionLogsInput().run(sys.argv))

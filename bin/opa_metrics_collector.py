#!/usr/bin/env python3
"""
OPA Metrics Collector Modular Input

This modular input collects performance metrics, query statistics,
and operational data from OPA instances for analytics and monitoring.

Author: OPA Community
Version: 1.0.0
"""

import concurrent.futures
import json
import logging
import os
import re
import sys
import time
from collections import defaultdict
from datetime import datetime, timezone
from urllib.parse import urljoin, urlparse

import requests

# Add the lib directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "lib"))

try:
    from splunklib.modularinput import *
    from splunklib.modularinput.event_writer import EventWriter
except ImportError as e:
    print(f"Error importing Splunk libraries: {e}", file=sys.stderr)
    sys.exit(1)


class OPAMetricsCollectorInput(Script):
    """Modular input for OPA metrics collection"""

    def get_scheme(self):
        """Define the input scheme"""
        scheme = Scheme("OPA Metrics Collector")
        scheme.description = "Collect performance metrics and operational data from OPA instances"
        scheme.use_external_validation = True
        scheme.use_single_instance = False

        # OPA Endpoints Configuration
        opa_endpoints_arg = Argument("opa_endpoints")
        opa_endpoints_arg.data_type = Argument.data_type_string
        opa_endpoints_arg.description = (
            "Comma-separated list of OPA endpoints to collect metrics from "
            "(e.g., http://opa1:8181,http://opa2:8181)"
        )
        opa_endpoints_arg.required_on_create = True
        opa_endpoints_arg.required_on_edit = True
        scheme.add_argument(opa_endpoints_arg)

        # Metrics Configuration
        metrics_endpoints_arg = Argument("metrics_endpoints")
        metrics_endpoints_arg.data_type = Argument.data_type_string
        metrics_endpoints_arg.description = (
            "Comma-separated list of metrics endpoints (default: /metrics for each OPA endpoint)"
        )
        metrics_endpoints_arg.required_on_create = False
        scheme.add_argument(metrics_endpoints_arg)

        # Collection Configuration
        collect_prometheus_arg = Argument("collect_prometheus")
        collect_prometheus_arg.data_type = Argument.data_type_boolean
        collect_prometheus_arg.description = "Collect Prometheus-style metrics"
        collect_prometheus_arg.required_on_create = False
        scheme.add_argument(collect_prometheus_arg)

        collect_query_stats_arg = Argument("collect_query_stats")
        collect_query_stats_arg.data_type = Argument.data_type_boolean
        collect_query_stats_arg.description = "Collect query performance statistics"
        collect_query_stats_arg.required_on_create = False
        scheme.add_argument(collect_query_stats_arg)

        collect_bundle_stats_arg = Argument("collect_bundle_stats")
        collect_bundle_stats_arg.data_type = Argument.data_type_boolean
        collect_bundle_stats_arg.description = "Collect bundle download and activation statistics"
        collect_bundle_stats_arg.required_on_create = False
        scheme.add_argument(collect_bundle_stats_arg)

        # Request Configuration
        request_timeout_arg = Argument("request_timeout")
        request_timeout_arg.data_type = Argument.data_type_number
        request_timeout_arg.description = "HTTP request timeout in seconds"
        request_timeout_arg.required_on_create = False
        scheme.add_argument(request_timeout_arg)

        # Filtering Configuration
        metric_filters_arg = Argument("metric_filters")
        metric_filters_arg.data_type = Argument.data_type_string
        metric_filters_arg.description = (
            "Comma-separated list of metric name patterns to include (regex supported)"
        )
        metric_filters_arg.required_on_create = False
        scheme.add_argument(metric_filters_arg)

        # Authentication
        auth_token_arg = Argument("auth_token")
        auth_token_arg.data_type = Argument.data_type_string
        auth_token_arg.description = "Authentication token for OPA API (if required)"
        auth_token_arg.required_on_create = False
        scheme.add_argument(auth_token_arg)

        # Logging Configuration
        log_level_arg = Argument("log_level")
        log_level_arg.data_type = Argument.data_type_string
        log_level_arg.description = "Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)"
        log_level_arg.required_on_create = False
        scheme.add_argument(log_level_arg)

        return scheme

    def validate_input(self, validation_definition):
        """Validate the input configuration"""
        try:
            parameters = validation_definition.parameters

            # Validate OPA endpoints
            endpoints_str = parameters.get("opa_endpoints", "")
            if not endpoints_str:
                raise ValueError("OPA endpoints are required")

            endpoints = [ep.strip() for ep in endpoints_str.split(",") if ep.strip()]
            if not endpoints:
                raise ValueError("At least one OPA endpoint must be specified")

            for endpoint in endpoints:
                parsed_url = urlparse(endpoint)
                if not parsed_url.scheme or not parsed_url.netloc:
                    raise ValueError(f"Invalid endpoint URL format: {endpoint}")
                if not parsed_url.scheme.startswith("http"):
                    raise ValueError(f"Endpoint must use HTTP or HTTPS: {endpoint}")

            # Validate numeric parameters
            timeout = int(parameters.get("request_timeout", 10))
            if timeout < 1 or timeout > 300:
                raise ValueError("Request timeout must be between 1 and 300 seconds")

            # Validate metric filters (regex patterns)
            filters_str = parameters.get("metric_filters", "")
            if filters_str:
                filters = [f.strip() for f in filters_str.split(",") if f.strip()]
                for filter_pattern in filters:
                    try:
                        re.compile(filter_pattern)
                    except re.error as e:
                        raise ValueError(f"Invalid regex pattern '{filter_pattern}': {str(e)}")

            # Validate log level
            log_level = parameters.get("log_level", "INFO").upper()
            if log_level not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
                raise ValueError("Invalid log level")

            # Test connectivity to OPA endpoints
            self.test_opa_connectivity(endpoints, timeout, parameters.get("auth_token", ""))

        except Exception as e:
            raise Exception(f"Validation failed: {str(e)}")

    def test_opa_connectivity(self, endpoints, timeout, auth_token):
        """Test connectivity to OPA endpoints"""
        headers = {"User-Agent": "Splunk-OPA-Addon/1.0.0"}
        if auth_token:
            headers["Authorization"] = f"Bearer {auth_token}"

        failed_endpoints = []

        for endpoint in endpoints:
            try:
                metrics_url = urljoin(endpoint, "/metrics")
                response = requests.get(metrics_url, headers=headers, timeout=timeout)

                if response.status_code >= 400:
                    failed_endpoints.append(f"{endpoint} (HTTP {response.status_code})")

            except requests.exceptions.RequestException as e:
                failed_endpoints.append(f"{endpoint} ({str(e)})")

        if failed_endpoints:
            raise ValueError(
                f"Cannot connect to OPA metrics endpoints: {', '.join(failed_endpoints)}"
            )

    def stream_events(self, inputs, ew):
        """Main execution method"""
        self.service.namespace["app"] = "opa_policy_audit_addon"

        for input_name, input_item in inputs.inputs.items():
            try:
                # Extract configuration
                config = {
                    "opa_endpoints": [
                        ep.strip()
                        for ep in input_item.get("opa_endpoints", "").split(",")
                        if ep.strip()
                    ],
                    "metrics_endpoints": (
                        [
                            ep.strip()
                            for ep in input_item.get("metrics_endpoints", "").split(",")
                            if ep.strip()
                        ]
                        if input_item.get("metrics_endpoints")
                        else None
                    ),
                    "collect_prometheus": input_item.get("collect_prometheus", "1").lower()
                    in ("1", "true", "yes"),
                    "collect_query_stats": input_item.get("collect_query_stats", "1").lower()
                    in ("1", "true", "yes"),
                    "collect_bundle_stats": input_item.get("collect_bundle_stats", "1").lower()
                    in ("1", "true", "yes"),
                    "request_timeout": int(input_item.get("request_timeout", 10)),
                    "metric_filters": (
                        [
                            f.strip()
                            for f in input_item.get("metric_filters", "").split(",")
                            if f.strip()
                        ]
                        if input_item.get("metric_filters")
                        else None
                    ),
                    "auth_token": input_item.get("auth_token", ""),
                    "log_level": input_item.get("log_level", "INFO").upper(),
                    "input_name": input_name,
                    "index": input_item.get("index", "main"),
                    "sourcetype": input_item.get("sourcetype", "opa:metrics"),
                    "host": input_item.get("host", "opa-metrics"),
                }

                # Setup logging
                self.setup_logging(config["log_level"])

                # Start metrics collection
                collector = OPAMetricsCollector(config, ew, self.logger)
                collector.run()

            except Exception as e:
                ew.log(EventWriter.ERROR, f"Error starting OPA metrics collector: {str(e)}")
                raise

    def setup_logging(self, log_level):
        """Setup logging configuration"""
        self.logger = logging.getLogger("opa_metrics_collector")
        self.logger.setLevel(getattr(logging, log_level))

        if not self.logger.handlers:
            handler = logging.StreamHandler(sys.stderr)
            formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)


class OPAMetricsCollector:
    """OPA metrics collection implementation"""

    def __init__(self, config, event_writer, logger):
        self.config = config
        self.event_writer = event_writer
        self.logger = logger
        self.session = requests.Session()
        self.metric_filters = None

        # Setup session headers
        headers = {"User-Agent": "Splunk-OPA-Addon/1.0.0"}
        if config["auth_token"]:
            headers["Authorization"] = f'Bearer {config["auth_token"]}'
        self.session.headers.update(headers)

        # Compile metric filters
        if config["metric_filters"]:
            self.metric_filters = [re.compile(pattern) for pattern in config["metric_filters"]]

    def run(self):
        """Main collection loop"""
        self.logger.info(
            f"Starting OPA metrics collection for {len(self.config['opa_endpoints'])} endpoints"
        )

        try:
            while True:
                start_time = time.time()

                # Collect metrics from all endpoints
                self.collect_metrics()

                # Log collection cycle completion
                elapsed_time = time.time() - start_time
                self.logger.debug(f"Metrics collection cycle completed in {elapsed_time:.2f}s")

                # Sleep until next collection cycle
                time.sleep(60)  # Default 60 seconds, will be overridden by Splunk's interval

        except KeyboardInterrupt:
            self.logger.info("Metrics collection interrupted by user")
        except Exception as e:
            self.logger.error(f"Error in collection loop: {str(e)}")
            raise

    def collect_metrics(self):
        """Collect metrics from all configured endpoints"""
        # Use thread pool for concurrent collection
        with concurrent.futures.ThreadPoolExecutor(
            max_workers=min(10, len(self.config["opa_endpoints"]))
        ) as executor:
            future_to_endpoint = {
                executor.submit(self.collect_endpoint_metrics, endpoint): endpoint
                for endpoint in self.config["opa_endpoints"]
            }

            for future in concurrent.futures.as_completed(future_to_endpoint):
                endpoint = future_to_endpoint[future]
                try:
                    future.result()
                except Exception as e:
                    self.logger.error(
                        f"Error collecting metrics from endpoint {endpoint}: {str(e)}"
                    )

    def collect_endpoint_metrics(self, endpoint):
        """Collect metrics from a single OPA endpoint"""
        try:
            timestamp = datetime.now(timezone.utc).isoformat()

            # Collect different types of metrics
            if self.config["collect_prometheus"]:
                self.collect_prometheus_metrics(endpoint, timestamp)

            if self.config["collect_query_stats"]:
                self.collect_query_statistics(endpoint, timestamp)

            if self.config["collect_bundle_stats"]:
                self.collect_bundle_statistics(endpoint, timestamp)

        except Exception as e:
            self.logger.error(f"Error collecting metrics from endpoint {endpoint}: {str(e)}")
            # Send error event
            error_data = {
                "endpoint": endpoint,
                "metric_type": "collection_error",
                "error_message": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
            self.send_metrics_event(error_data)

    def collect_prometheus_metrics(self, endpoint, timestamp):
        """Collect Prometheus-style metrics"""
        try:
            # Determine metrics endpoint
            if self.config["metrics_endpoints"]:
                # Use custom metrics endpoints if provided
                for metrics_endpoint in self.config["metrics_endpoints"]:
                    if not metrics_endpoint.startswith("http"):
                        metrics_url = urljoin(endpoint, metrics_endpoint)
                    else:
                        metrics_url = metrics_endpoint

                    self._collect_prometheus_from_url(metrics_url, endpoint, timestamp)
            else:
                # Use default /metrics endpoint
                metrics_url = urljoin(endpoint, "/metrics")
                self._collect_prometheus_from_url(metrics_url, endpoint, timestamp)

        except Exception as e:
            self.logger.error(f"Error collecting Prometheus metrics from {endpoint}: {str(e)}")

    def _collect_prometheus_from_url(self, metrics_url, endpoint, timestamp):
        """Collect Prometheus metrics from a specific URL"""
        try:
            response = self.session.get(metrics_url, timeout=self.config["request_timeout"])

            if response.status_code == 200:
                metrics_text = response.text
                parsed_metrics = self.parse_prometheus_metrics(metrics_text)

                # Filter metrics if filters are configured
                if self.metric_filters:
                    filtered_metrics = {}
                    for metric_name, metric_data in parsed_metrics.items():
                        if any(pattern.match(metric_name) for pattern in self.metric_filters):
                            filtered_metrics[metric_name] = metric_data
                    parsed_metrics = filtered_metrics

                # Send metrics events
                for metric_name, metric_data in parsed_metrics.items():
                    event_data = {
                        "endpoint": endpoint,
                        "metric_type": "prometheus",
                        "metric_name": metric_name,
                        "metric_value": metric_data["value"],
                        "metric_labels": metric_data.get("labels", {}),
                        "metric_help": metric_data.get("help", ""),
                        "metric_type_info": metric_data.get("type", ""),
                        "timestamp": timestamp,
                    }
                    self.send_metrics_event(event_data)

            else:
                self.logger.warning(
                    f"Failed to collect Prometheus metrics from {metrics_url}: "
                    f"HTTP {response.status_code}"
                )

        except requests.exceptions.RequestException as e:
            self.logger.error(
                f"Request error collecting Prometheus metrics from {metrics_url}: {str(e)}"
            )

    def parse_prometheus_metrics(self, metrics_text):
        """Parse Prometheus-style metrics text"""
        metrics = {}
        current_help = {}
        current_type = {}

        for line in metrics_text.split("\n"):
            line = line.strip()
            if not line:
                continue

            # Parse HELP lines
            if line.startswith("# HELP "):
                parts = line[7:].split(" ", 1)
                if len(parts) == 2:
                    metric_name, help_text = parts
                    current_help[metric_name] = help_text

            # Parse TYPE lines
            elif line.startswith("# TYPE "):
                parts = line[7:].split(" ", 1)
                if len(parts) == 2:
                    metric_name, metric_type = parts
                    current_type[metric_name] = metric_type

            # Parse metric lines
            elif not line.startswith("#"):
                try:
                    # Split metric line into name/labels and value
                    if " " in line:
                        metric_part, value_part = line.rsplit(" ", 1)

                        # Parse metric name and labels
                        if "{" in metric_part and "}" in metric_part:
                            metric_name = metric_part.split("{")[0]
                            labels_part = metric_part[
                                metric_part.index("{") : metric_part.rindex("}") + 1
                            ]
                            labels = self.parse_prometheus_labels(labels_part)
                        else:
                            metric_name = metric_part
                            labels = {}

                        # Parse value
                        try:
                            value = float(value_part)
                        except ValueError:
                            continue

                        # Create metric entry
                        metric_key = f"{metric_name}_{hash(str(sorted(labels.items())))}"
                        metrics[metric_key] = {
                            "name": metric_name,
                            "value": value,
                            "labels": labels,
                            "help": current_help.get(metric_name, ""),
                            "type": current_type.get(metric_name, ""),
                        }

                except Exception as e:
                    self.logger.debug(f"Error parsing metric line '{line}': {str(e)}")
                    continue

        return metrics

    def parse_prometheus_labels(self, labels_str):
        """Parse Prometheus labels string"""
        labels = {}

        # Remove outer braces
        labels_str = labels_str.strip("{}").strip()

        if not labels_str:
            return labels

        # Simple label parsing (handles basic cases)
        # For production, consider using a proper Prometheus parser
        try:
            # Split by comma, but be careful with quoted values
            parts = []
            current_part = ""
            in_quotes = False

            for char in labels_str:
                if char == '"' and (not current_part or current_part[-1] != "\\"):
                    in_quotes = not in_quotes
                elif char == "," and not in_quotes:
                    parts.append(current_part.strip())
                    current_part = ""
                    continue
                current_part += char

            if current_part.strip():
                parts.append(current_part.strip())

            # Parse each label
            for part in parts:
                if "=" in part:
                    key, value = part.split("=", 1)
                    key = key.strip()
                    value = value.strip().strip('"')
                    labels[key] = value

        except Exception as e:
            self.logger.debug(f"Error parsing labels '{labels_str}': {str(e)}")

        return labels

    def collect_query_statistics(self, endpoint, timestamp):
        """Collect query performance statistics"""
        try:
            # Try to get query stats from OPA's status endpoint
            status_url = urljoin(endpoint, "/v1/status")

            response = self.session.get(status_url, timeout=self.config["request_timeout"])

            if response.status_code == 200:
                status_data = response.json()

                # Extract query-related metrics
                query_stats = {
                    "endpoint": endpoint,
                    "metric_type": "query_statistics",
                    "timestamp": timestamp,
                }

                # Add available query metrics
                result = status_data.get("result", {})
                if "metrics" in result:
                    metrics = result["metrics"]

                    # Extract query-related metrics
                    for key, value in metrics.items():
                        if "query" in key.lower() or "eval" in key.lower():
                            query_stats[f"metric_{key}"] = value

                if len(query_stats) > 3:  # More than just the base fields
                    self.send_metrics_event(query_stats)

            else:
                self.logger.debug(
                    f"Failed to collect query statistics from {endpoint}: "
                    f"HTTP {response.status_code}"
                )

        except requests.exceptions.RequestException as e:
            self.logger.debug(
                f"Request error collecting query statistics from {endpoint}: {str(e)}"
            )

    def collect_bundle_statistics(self, endpoint, timestamp):
        """Collect bundle download and activation statistics"""
        try:
            bundles_url = urljoin(endpoint, "/v1/status/bundles")

            response = self.session.get(bundles_url, timeout=self.config["request_timeout"])

            if response.status_code == 200:
                bundle_status = response.json()
                bundles = bundle_status.get("result", {})

                for bundle_name, bundle_info in bundles.items():
                    bundle_stats = {
                        "endpoint": endpoint,
                        "metric_type": "bundle_statistics",
                        "bundle_name": bundle_name,
                        "active_revision": bundle_info.get("active_revision"),
                        "last_successful_download": bundle_info.get("last_successful_download"),
                        "last_successful_activation": bundle_info.get("last_successful_activation"),
                        "last_request": bundle_info.get("last_request"),
                        "code": bundle_info.get("code"),
                        "message": bundle_info.get("message", ""),
                        "timestamp": timestamp,
                    }

                    # Add metrics if available
                    if "metrics" in bundle_info:
                        for metric_name, metric_value in bundle_info["metrics"].items():
                            bundle_stats[f"metric_{metric_name}"] = metric_value

                    self.send_metrics_event(bundle_stats)

            else:
                self.logger.debug(
                    f"Failed to collect bundle statistics from {endpoint}: "
                    f"HTTP {response.status_code}"
                )

        except requests.exceptions.RequestException as e:
            self.logger.debug(
                f"Request error collecting bundle statistics from {endpoint}: {str(e)}"
            )

    def send_metrics_event(self, metrics_data):
        """Send metrics event to Splunk"""
        try:
            # Create Splunk event
            event = Event(
                data=json.dumps(metrics_data),
                time=time.time(),
                index=self.config["index"],
                sourcetype=self.config["sourcetype"],
                host=self.config["host"],
                source=f"opa_metrics_collector:{self.config['input_name']}",
            )

            # Write event to Splunk
            self.event_writer.write_event(event)

        except Exception as e:
            self.logger.error(f"Error sending metrics event: {str(e)}")


if __name__ == "__main__":
    sys.exit(OPAMetricsCollectorInput().run(sys.argv))

#!/bin/bash

echo "Loading sample data for OPA Policy Audit demo..."

# Create sample data directory
mkdir -p /tmp/sample_data

# Generate OPA Decision Logs sample data
cat > /tmp/sample_data/opa_decisions.json << 'EOF'
{"timestamp":"2025-01-21T10:00:00.000Z","decision_id":"dec_001","input":{"user":"john.doe","method":"GET","path":"/api/users","resource":"users"},"result":true,"policy_name":"user_access_policy","response_time":0.025,"client_ip":"*************"}
{"timestamp":"2025-01-21T10:01:00.000Z","decision_id":"dec_002","input":{"user":"jane.smith","method":"POST","path":"/api/admin","resource":"admin"},"result":false,"policy_name":"admin_access_policy","response_time":0.030,"client_ip":"*************"}
{"timestamp":"2025-01-21T10:02:00.000Z","decision_id":"dec_003","input":{"user":"bob.wilson","method":"GET","path":"/api/data","resource":"data"},"result":true,"policy_name":"data_access_policy","response_time":0.020,"client_ip":"*************"}
{"timestamp":"2025-01-21T10:03:00.000Z","decision_id":"dec_004","input":{"user":"alice.brown","method":"DELETE","path":"/api/users/123","resource":"users"},"result":true,"policy_name":"user_management_policy","response_time":0.035,"client_ip":"*************"}
{"timestamp":"2025-01-21T10:04:00.000Z","decision_id":"dec_005","input":{"user":"charlie.davis","method":"POST","path":"/api/admin/config","resource":"config"},"result":false,"policy_name":"config_access_policy","response_time":0.028,"client_ip":"*************"}
{"timestamp":"2025-01-21T10:05:00.000Z","decision_id":"dec_006","input":{"user":"john.doe","method":"POST","path":"/api/admin","resource":"admin"},"result":false,"policy_name":"admin_access_policy","response_time":0.032,"client_ip":"*************"}
{"timestamp":"2025-01-21T10:06:00.000Z","decision_id":"dec_007","input":{"user":"john.doe","method":"POST","path":"/api/admin","resource":"admin"},"result":false,"policy_name":"admin_access_policy","response_time":0.029,"client_ip":"*************"}
{"timestamp":"2025-01-21T10:07:00.000Z","decision_id":"dec_008","input":{"user":"john.doe","method":"POST","path":"/api/admin","resource":"admin"},"result":false,"policy_name":"admin_access_policy","response_time":0.031,"client_ip":"*************"}
EOF

# Generate OPA Health sample data
cat > /tmp/sample_data/opa_health.json << 'EOF'
{"timestamp":"2025-01-21T10:00:00.000Z","host":"opa-instance-1","status":"ok","uptime":86400,"memory_usage":256,"cpu_usage":15.5,"bundle_status":"active","plugin_status":"healthy"}
{"timestamp":"2025-01-21T10:05:00.000Z","host":"opa-instance-2","status":"ok","uptime":86400,"memory_usage":312,"cpu_usage":22.3,"bundle_status":"active","plugin_status":"healthy"}
{"timestamp":"2025-01-21T10:10:00.000Z","host":"opa-instance-3","status":"warning","uptime":86400,"memory_usage":456,"cpu_usage":45.7,"bundle_status":"active","plugin_status":"degraded"}
EOF

# Generate OPA Metrics sample data
cat > /tmp/sample_data/opa_metrics.json << 'EOF'
{"timestamp":"2025-01-21T10:00:00.000Z","instance":"opa-instance-1","metric_name":"http_request_duration_seconds","value":0.025,"labels":{"method":"POST","path":"/v1/data"}}
{"timestamp":"2025-01-21T10:00:00.000Z","instance":"opa-instance-1","metric_name":"opa_policy_evaluation_duration_seconds","value":0.015,"labels":{"policy":"user_access_policy"}}
{"timestamp":"2025-01-21T10:00:00.000Z","instance":"opa-instance-1","metric_name":"opa_bundle_loaded_total","value":1,"labels":{"bundle":"main"}}
EOF

# Generate Styra DAS Audit sample data
cat > /tmp/sample_data/styra_audit.json << 'EOF'
{"timestamp":"2025-01-21T09:30:00.000Z","user_id":"<EMAIL>","action":"policy_update","system_id":"sys_001","policy_id":"user_access_policy","change_description":"Updated user access rules","risk_level":"medium"}
{"timestamp":"2025-01-21T09:45:00.000Z","user_id":"<EMAIL>","action":"policy_create","system_id":"sys_001","policy_id":"new_security_policy","change_description":"Created new security policy","risk_level":"high"}
{"timestamp":"2025-01-21T10:00:00.000Z","user_id":"<EMAIL>","action":"bundle_publish","system_id":"sys_001","bundle_id":"bundle_v1.2.3","change_description":"Published updated policy bundle","risk_level":"low"}
EOF

echo "Sample data files created. Loading into Splunk..."

# Load data into Splunk indexes using oneshot
/opt/splunk/bin/splunk add oneshot /tmp/sample_data/opa_decisions.json -index opa_decisions -sourcetype "opa:decision" -auth admin:buildathon2025
/opt/splunk/bin/splunk add oneshot /tmp/sample_data/opa_health.json -index opa_health -sourcetype "opa:health" -auth admin:buildathon2025
/opt/splunk/bin/splunk add oneshot /tmp/sample_data/opa_metrics.json -index opa_metrics -sourcetype "opa:metrics" -auth admin:buildathon2025
/opt/splunk/bin/splunk add oneshot /tmp/sample_data/styra_audit.json -index styra_audit -sourcetype "styra:das:policy:audit" -auth admin:buildathon2025

echo "Sample data loaded successfully!"

# Generate additional historical data for better demo
echo "Generating additional historical data..."

# Create a script to generate more sample data
python3 << 'PYTHON_EOF'
import json
import datetime
import random

# Generate more OPA decisions for the last 24 hours
decisions = []
users = ["john.doe", "jane.smith", "bob.wilson", "alice.brown", "charlie.davis", "diana.miller", "eve.taylor"]
policies = ["user_access_policy", "admin_access_policy", "data_access_policy", "config_access_policy", "api_access_policy"]
methods = ["GET", "POST", "PUT", "DELETE"]
paths = ["/api/users", "/api/admin", "/api/data", "/api/config", "/api/reports"]

base_time = datetime.datetime.now() - datetime.timedelta(hours=24)

for i in range(1000):
    timestamp = base_time + datetime.timedelta(minutes=random.randint(0, 1440))
    user = random.choice(users)
    policy = random.choice(policies)
    method = random.choice(methods)
    path = random.choice(paths)
    
    # Simulate some failed attempts for security demo
    if user == "john.doe" and policy == "admin_access_policy":
        result = False if random.random() < 0.8 else True
    else:
        result = True if random.random() < 0.9 else False
    
    decision = {
        "timestamp": timestamp.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
        "decision_id": f"dec_{i+100:04d}",
        "input": {
            "user": user,
            "method": method,
            "path": path,
            "resource": path.split("/")[-1]
        },
        "result": result,
        "policy_name": policy,
        "response_time": round(random.uniform(0.010, 0.050), 3),
        "client_ip": f"192.168.1.{random.randint(100, 200)}"
    }
    decisions.append(decision)

# Write to file
with open('/tmp/sample_data/opa_decisions_bulk.json', 'w') as f:
    for decision in decisions:
        f.write(json.dumps(decision) + '\n')

print(f"Generated {len(decisions)} additional decision records")
PYTHON_EOF

# Load the bulk data
/opt/splunk/bin/splunk add oneshot /tmp/sample_data/opa_decisions_bulk.json -index opa_decisions -sourcetype "opa:decision" -auth admin:buildathon2025

echo "Historical sample data loaded!"

# Clean up
rm -rf /tmp/sample_data

echo "Sample data loading complete!"

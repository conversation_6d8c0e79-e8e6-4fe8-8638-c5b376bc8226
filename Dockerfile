FROM splunk/splunk:latest

# Set environment variables for Splunk
ENV SPLUNK_START_ARGS="--accept-license"
ENV SPLUNK_PASSWORD="buildathon2025"
ENV SPLUNK_USER="admin"
ENV SPLUNK_ENABLE_LISTEN="9997"

# Switch to root to install packages and setup
USER root

# Install required packages
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Create directories
RUN mkdir -p /opt/splunk/etc/apps/opa_policy_audit_addon
RUN mkdir -p /opt/splunk/var/lib/splunk

# Copy the add-on
COPY dist/opa_policy_audit_addon /opt/splunk/etc/apps/opa_policy_audit_addon/

# Copy initialization scripts
COPY init-splunk.sh /opt/splunk/bin/init-splunk.sh
COPY sample-data.sh /opt/splunk/bin/sample-data.sh

# Make scripts executable
RUN chmod +x /opt/splunk/bin/init-splunk.sh
RUN chmod +x /opt/splunk/bin/sample-data.sh

# Set ownership
RUN chown -R splunk:splunk /opt/splunk/etc/apps/opa_policy_audit_addon
RUN chown -R splunk:splunk /opt/splunk/var/lib/splunk
RUN chown splunk:splunk /opt/splunk/bin/init-splunk.sh
RUN chown splunk:splunk /opt/splunk/bin/sample-data.sh

# Switch back to splunk user
USER splunk

# Expose Splunk web port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=120s --retries=3 \
  CMD curl -f http://localhost:8000/en-US/account/login || exit 1

# Start Splunk with initialization
CMD ["/opt/splunk/bin/init-splunk.sh"]

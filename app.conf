#
# OPA Policy Audit & Compliance Add-on for Splunk
# Application Configuration File
#
# This file defines the core metadata, capabilities, and configuration
# for the Splunk add-on that provides comprehensive OPA policy monitoring,
# audit, and compliance capabilities.
#

[install]
# Installation state and requirements
state = enabled
is_configured = false
build = 1

[launcher]
# App launcher configuration
author = OPA Community
description = Comprehensive Splunk add-on for Open Policy Agent (OPA) policy audit, compliance monitoring, and security analytics. Provides real-time policy decision logging, Styra DAS integration, health monitoring, and advanced security dashboards.
version = 1.0.0

[ui]
# User interface settings
is_visible = true
label = OPA Policy Audit & Compliance

[package]
# Package metadata
id = opa_policy_audit_addon
check_for_updates = true

[triggers]
# Reload triggers for configuration changes
reload.inputs = simple
reload.transforms = simple
reload.props = simple
reload.tags = simple
reload.eventtypes = simple
reload.macros = simple
reload.savedsearches = simple
reload.workflow_actions = simple
reload.addon_builder = simple
reload.ta_opa_policy_audit_addon_settings = simple
reload.opa_addon_settings = simple
reload.passwords = simple

[credential]
# Credential management settings
password_endpoint = https://localhost:8089/services/storage/passwords

[capabilities]
# Required capabilities for the add-on
requires_capabilities = admin_all_objects,edit_tcp,edit_udp,edit_scripted,list_inputs,edit_modinput_opa_decision_logs,edit_modinput_styra_das_audit,edit_modinput_opa_health_monitor,edit_modinput_opa_metrics_collector



[id]
# Unique identifier settings
name = opa_policy_audit_addon
version = 1.0.0

[shclustering]
# Search head clustering support
deployer_push_mode = always_push
deployer_lookups_push_mode = preserve_lookups

[replication]
# Replication settings for distributed environments
factor = auto

[diag]
# Diagnostic settings
EXCLUDE-$SPLUNK_HOME/etc/apps/opa_policy_audit_addon/local = *.conf
EXCLUDE-$SPLUNK_HOME/etc/apps/opa_policy_audit_addon/metadata = *.meta

# Custom stanzas for OPA-specific configuration

[opa_settings]
# Global OPA configuration settings
default_timeout = 30
max_retries = 3
backoff_factor = 2.0
max_connections = 100
buffer_size = 8192
compression_enabled = true
ssl_verify = true
health_check_interval = 60
metrics_collection_interval = 300

[styra_das_settings]
# Styra DAS specific settings
api_version = v1
default_polling_interval = 60
max_events_per_request = 1000
checkpoint_file = styra_das_checkpoint.json
api_timeout = 30
retry_on_failure = true

[security_settings]
# Security and compliance settings
enable_threat_detection = true
enable_anomaly_detection = true
enable_compliance_monitoring = true
risk_threshold_high = 8.0
risk_threshold_medium = 5.0
risk_threshold_low = 2.0
max_failed_attempts = 5
lockout_duration = 300

[performance_settings]
# Performance optimization settings
max_concurrent_requests = 50
request_queue_size = 1000
worker_threads = 4
memory_limit_mb = 512
cpu_limit_percent = 80
gc_threshold = 0.8

[logging_settings]
# Logging configuration
log_level = INFO
log_rotation_size = 10MB
log_retention_days = 30
max_log_files = 10
enable_debug_logging = false
log_sensitive_data = false

[data_retention]
# Data retention policies
default_retention_days = 90
audit_retention_days = 2555  # 7 years for compliance
metrics_retention_days = 365
health_retention_days = 30
alert_retention_days = 180

[compliance_frameworks]
# Supported compliance frameworks
sox_enabled = true
pci_enabled = true
hipaa_enabled = true
gdpr_enabled = true
iso27001_enabled = true
nist_enabled = true
cis_enabled = true

[alerting]
# Alerting configuration
enable_email_alerts = true
enable_webhook_alerts = true
enable_splunk_alerts = true
default_alert_severity = medium
max_alerts_per_hour = 100
alert_throttling_enabled = true

[integration]
# External integration settings
jira_integration_enabled = false
slack_integration_enabled = false
teams_integration_enabled = false
siem_integration_enabled = true
threat_intel_enabled = true

[dashboard_settings]
# Dashboard configuration
default_time_range = -24h@h
max_search_results = 10000
refresh_interval = 300
enable_drilldown = true
enable_export = true

[search_settings]
# Search optimization settings
max_search_time = 300
search_timeout = 600
max_concurrent_searches = 10
enable_search_acceleration = true
summary_indexing = true

[index_settings]
# Index configuration recommendations
default_index = opa_audit
metrics_index = opa_metrics
health_index = opa_health
security_index = opa_security
compliance_index = opa_compliance

[field_extraction]
# Field extraction settings
enable_auto_kv = true
enable_json_extraction = true
enable_timestamp_extraction = true
max_field_count = 1000
field_extraction_timeout = 30

[cim_compliance]
# Common Information Model compliance
enable_cim_tagging = true
enable_cim_normalization = true
cim_version = 4.20.0
authentication_model = enabled
change_model = enabled
malware_model = enabled
network_traffic_model = enabled
vulnerabilities_model = enabled

[machine_learning]
# Machine learning settings
enable_mltk = true
enable_anomaly_detection = true
model_training_interval = 86400  # 24 hours
model_retention_days = 30
min_training_data_points = 1000

[api_settings]
# REST API configuration
enable_rest_api = true
api_version = v1
max_api_requests_per_minute = 1000
api_authentication_required = true
api_ssl_required = true

[backup_settings]
# Backup and recovery settings
enable_config_backup = true
backup_interval_hours = 24
max_backup_files = 7
backup_compression = true

[monitoring]
# Internal monitoring settings
enable_self_monitoring = true
health_check_enabled = true
performance_monitoring = true
resource_monitoring = true
metrics_export_enabled = true

[experimental]
# Experimental features (use with caution)
enable_experimental_features = false
advanced_analytics = false
real_time_streaming = false
enhanced_visualization = false

# Version and build information
[build_info]
build_number = 1
build_date = 2024-01-15
build_commit = main
builder = automated
target_splunk_version = 9.0+
compatible_versions = 8.2,9.0,9.1,9.2

[dependencies]
# Add-on dependencies
requires_splunk_version = 8.2.0
requires_python_version = 3.7
optional_apps = Splunk_ML_Toolkit,Splunk_Security_Essentials,Common_Information_Model
conflicts_with = 

[support]
# Support information
support_url = https://github.com/your-org/opa-splunk-addon/issues
documentation_url = https://docs.example.com/opa-splunk-addon
community_url = https://community.openpolicyagent.org
contact_email = <EMAIL>

[license]
# License information
license_type = Apache-2.0
license_url = https://www.apache.org/licenses/LICENSE-2.0
copyright = Copyright 2024 OPA Community

[update]
# Update settings
check_for_updates = true
update_url = https://api.github.com/repos/your-org/opa-splunk-addon/releases/latest
auto_update = false
update_channel = stable

[credential_settings]
username = admin
password = <YOUR_SPLUNK_ADMIN_PASSWORD>
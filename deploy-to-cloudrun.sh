#!/bin/bash

echo "🚀 Deploying OPA Policy Audit Add-on to Google Cloud Run..."

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ Google Cloud SDK not found. Please install it first:"
    echo "curl https://sdk.cloud.google.com | bash"
    exit 1
fi

# Check if user is logged in
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo "🔐 Please login to Google Cloud:"
    gcloud auth login
fi

# Get or set project ID
PROJECT_ID=$(gcloud config get-value project)
if [ -z "$PROJECT_ID" ]; then
    echo "📝 Please set your Google Cloud project ID:"
    read -p "Enter your project ID: " PROJECT_ID
    gcloud config set project $PROJECT_ID
fi

echo "📋 Using project: $PROJECT_ID"

# Enable required APIs
echo "🔧 Enabling required Google Cloud APIs..."
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Set region
REGION="us-central1"
echo "🌍 Using region: $REGION"

# Build and deploy
echo "🏗️  Building and deploying to Cloud Run..."
echo "⏱️  This may take 5-10 minutes..."

gcloud run deploy splunk-opa-demo \
    --source . \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --port 8000 \
    --memory 4Gi \
    --cpu 2 \
    --timeout 3600 \
    --max-instances 1 \
    --set-env-vars="SPLUNK_PASSWORD=buildathon2025" \
    --set-env-vars="SPLUNK_USER=admin"

# Check deployment status
if [ $? -eq 0 ]; then
    echo "✅ Deployment successful!"
    
    # Get the service URL
    SERVICE_URL=$(gcloud run services describe splunk-opa-demo \
        --platform managed \
        --region $REGION \
        --format 'value(status.url)')
    
    echo ""
    echo "🎉 OPA Policy Audit Demo is now live!"
    echo "📱 Demo URL: $SERVICE_URL/en-US/app/opa_policy_audit_addon/"
    echo "🔑 Login: admin / buildathon2025"
    echo ""
    echo "📋 For buildathon submission:"
    echo "Demo Link: $SERVICE_URL/en-US/app/opa_policy_audit_addon/"
    echo "Login Credentials: admin / buildathon2025"
    echo ""
    echo "⏰ Note: The service may take 2-3 minutes to fully initialize on first access"
    echo "🔄 If you see a loading screen, please wait and refresh"
    
    # Save URL to file for easy access
    echo "$SERVICE_URL/en-US/app/opa_policy_audit_addon/" > demo_url.txt
    echo "💾 Demo URL saved to demo_url.txt"
    
else
    echo "❌ Deployment failed. Please check the error messages above."
    exit 1
fi

echo ""
echo "🎯 Next steps for buildathon:"
echo "1. Wait 2-3 minutes for full initialization"
echo "2. Access the demo URL above"
echo "3. Login with admin / buildathon2025"
echo "4. Navigate to the OPA Policy Audit & Compliance Add-on"
echo "5. Explore the dashboards with pre-loaded sample data"
echo ""
echo "📚 Documentation available in the app under 'docs/' folder"
echo "🎬 Ready for live demo with judges!"

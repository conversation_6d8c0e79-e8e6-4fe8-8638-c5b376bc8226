# OPA Policy Audit & Compliance Add-on - Installation Guide

## Overview

The OPA Policy Audit & Compliance Add-on provides comprehensive monitoring and auditing capabilities for Open Policy Agent (OPA) deployments and Styra DAS (Declarative Authorization Service) environments.

## Prerequisites

### System Requirements
- Splunk Enterprise 8.0+ or Splunk Cloud
- Python 3.7+ (included with S<PERSON>lunk)
- Network connectivity to OPA instances and Styra DAS (if applicable)

### Required Permissions
- `admin_all_objects` - For configuration management
- `edit_tcp`, `edit_udp` - For network inputs
- `edit_scripted` - For scripted inputs
- `list_inputs` - For input management
- Custom modular input permissions for OPA components

## Installation Methods

### Method 1: Splunkbase Installation (Recommended)
1. Navigate to Splunkbase and search for "OPA Policy Audit"
2. Click "Install" and follow the prompts
3. Restart Splunk if prompted

### Method 2: Manual Installation
1. Download the latest `.spl` package
2. In Splunk Web, go to **Apps** > **Manage Apps**
3. Click **Install app from file**
4. Upload the `.spl` file and click **Upload**
5. Restart Splunk if prompted

### Method 3: CLI Installation
```bash
$SPLUNK_HOME/bin/splunk install app /path/to/opa_policy_audit_addon.spl
$SPLUNK_HOME/bin/splunk restart
```

## Post-Installation Setup

### 1. Initial Configuration
1. Navigate to **Apps** > **OPA Policy Audit & Compliance Add-on**
2. Click **Set up** to access the configuration page
3. Configure your OPA instances and Styra DAS connections

### 2. Index Configuration
The add-on uses the following indexes:
- `opa_decisions` - OPA policy decision logs
- `opa_health` - OPA health and status data
- `opa_metrics` - OPA performance metrics
- `styra_audit` - Styra DAS audit logs

Create these indexes in Splunk:
```bash
# Create indexes via CLI
$SPLUNK_HOME/bin/splunk add index opa_decisions
$SPLUNK_HOME/bin/splunk add index opa_health
$SPLUNK_HOME/bin/splunk add index opa_metrics
$SPLUNK_HOME/bin/splunk add index styra_audit
```

Or via Splunk Web: **Settings** > **Indexes** > **New Index**

### 3. Configure Data Inputs

#### OPA Decision Logs
1. Go to **Settings** > **Data Inputs** > **OPA Decision Logs**
2. Click **New** to create a new input
3. Configure:
   - **Name**: Descriptive name for your OPA instance
   - **OPA Endpoint**: `http://your-opa-instance:8181`
   - **Polling Interval**: 60 seconds (recommended)
   - **Index**: `opa_decisions`

#### OPA Health Monitor
1. Go to **Settings** > **Data Inputs** > **OPA Health Monitor**
2. Configure health monitoring for each OPA instance
3. Set appropriate polling intervals (300 seconds recommended)

#### OPA Metrics Collector
1. Go to **Settings** > **Data Inputs** > **OPA Metrics Collector**
2. Configure metrics collection from OPA instances
3. Set polling interval based on your monitoring needs

#### Styra DAS Audit (Optional)
1. Go to **Settings** > **Data Inputs** > **Styra DAS Audit**
2. Configure your Styra DAS connection:
   - **API Token**: Your Styra DAS API token
   - **System ID**: Target system identifier
   - **Polling Interval**: 300 seconds (recommended)

## Verification

### 1. Check Data Ingestion
Run these searches to verify data is being collected:

```spl
# OPA Decision Logs
index=opa_decisions sourcetype="opa:decision" | head 10

# OPA Health Data
index=opa_health sourcetype="opa:health" | head 10

# OPA Metrics
index=opa_metrics sourcetype="opa:metrics" | head 10

# Styra DAS Audit (if configured)
index=styra_audit sourcetype="styra:das:policy:audit" | head 10
```

### 2. Verify Dashboards
1. Navigate to the **OPA Policy Audit Overview** dashboard
2. Confirm data is populating in all panels
3. Check the **OPA Security Dashboard** for security-related metrics

### 3. Test Alerts
1. Go to **Settings** > **Searches, reports, and alerts**
2. Verify that OPA-related saved searches are enabled
3. Test alert functionality with sample data

## Troubleshooting

### Common Issues

#### No Data Appearing
1. Check input configuration in **Settings** > **Data Inputs**
2. Verify OPA endpoints are accessible from Splunk
3. Check `_internal` index for input errors:
   ```spl
   index=_internal source=*opa* ERROR
   ```

#### Authentication Errors
1. Verify API tokens for Styra DAS
2. Check network connectivity and firewall rules
3. Validate OPA endpoint URLs and ports

#### Performance Issues
1. Adjust polling intervals for inputs
2. Monitor resource usage in **Settings** > **Monitoring Console**
3. Consider data retention policies for high-volume environments

### Log Locations
- Input logs: `$SPLUNK_HOME/var/log/splunk/splunkd.log`
- Python script logs: `$SPLUNK_HOME/var/log/splunk/python.log`
- Add-on specific logs: Search `index=_internal source=*opa*`

## Next Steps

1. Review the [Configuration Guide](CONFIGURATION.md) for detailed setup options
2. Explore the [User Guide](USER_GUIDE.md) for dashboard and search examples
3. Set up alerting based on your organization's requirements
4. Configure data retention policies for OPA indexes

## Support

For issues and questions:
- Check the [Troubleshooting Guide](TROUBLESHOOTING.md)
- Review Splunk logs for error messages
- Contact your Splunk administrator
- Submit issues on the project repository

## Security Considerations

- Use HTTPS endpoints when possible
- Secure API tokens and credentials
- Implement appropriate network segmentation
- Regular review of access permissions
- Monitor for unauthorized access attempts

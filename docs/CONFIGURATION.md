# OPA Policy Audit & Compliance Add-on - Configuration Guide

## Overview

This guide provides detailed configuration instructions for the OPA Policy Audit & Compliance Add-on, including all data inputs, dashboards, and advanced settings.

## Configuration Files

### Main Configuration Files
- `default/inputs.conf` - Data input definitions
- `default/props.conf` - Data parsing and field extraction
- `default/transforms.conf` - Data transformation rules
- `default/savedsearches.conf` - Saved searches and alerts
- `default/datamodels.conf` - Common Information Model mappings

### Custom Configuration
- `default/opa_addon_settings.conf` - Add-on specific settings
- `default/app.conf` - Application metadata and settings

## Data Input Configuration

### 1. OPA Decision Logs Input

#### Basic Configuration
```ini
[opa_decision_logs://my_opa_instance]
opa_endpoint = https://opa.example.com:8181
polling_interval = 60
index = opa_decisions
disabled = 0
```

#### Advanced Options
```ini
[opa_decision_logs://production_opa]
opa_endpoint = https://prod-opa.company.com:8181
polling_interval = 30
batch_size = 1000
timeout = 30
ssl_verify = true
auth_token = your_auth_token_here
index = opa_decisions
sourcetype = opa:decision
disabled = 0

# Custom field mappings
decision_path = /v1/data/decisions
query_params = {"since": "last_timestamp"}
```

### 2. OPA Health Monitor Input

#### Configuration Options
```ini
[opa_health_monitor://opa_cluster_1]
opa_endpoint = https://opa-1.example.com:8181
health_endpoint = /health
polling_interval = 300
timeout = 15
index = opa_health
sourcetype = opa:health
disabled = 0

# Health check parameters
check_bundles = true
check_plugins = true
alert_on_failure = true
```

### 3. OPA Metrics Collector Input

#### Metrics Configuration
```ini
[opa_metrics_collector://opa_metrics]
opa_endpoint = https://opa.example.com:8181
metrics_endpoint = /metrics
polling_interval = 60
format = prometheus
index = opa_metrics
sourcetype = opa:metrics
disabled = 0

# Metric filtering
include_metrics = opa_.*,http_.*,bundle_.*
exclude_metrics = debug_.*,test_.*
```

### 4. Styra DAS Audit Input

#### DAS Configuration
```ini
[styra_das_audit://das_system_1]
das_endpoint = https://api.styra.com
api_token = your_das_api_token
system_id = your_system_id
polling_interval = 300
index = styra_audit
sourcetype = styra:das:policy:audit
disabled = 0

# Audit scope
audit_types = policy_changes,user_actions,system_events
start_time = -24h
```

## Field Extraction Configuration

### Custom Field Extractions

#### OPA Decision Fields
```ini
# In props.conf
[opa:decision]
EXTRACT-policy_name = "policy":"(?<policy_name>[^"]+)"
EXTRACT-decision_result = "result":(?<decision_result>true|false)
EXTRACT-user_context = "input":{[^}]*"user":"(?<user_context>[^"]+)"
EXTRACT-resource_path = "input":{[^}]*"path":"(?<resource_path>[^"]+)"
```

#### Styra DAS Fields
```ini
[styra:das:policy:audit]
EXTRACT-action_type = "action":"(?<action_type>[^"]+)"
EXTRACT-policy_id = "policy_id":"(?<policy_id>[^"]+)"
EXTRACT-user_id = "user_id":"(?<user_id>[^"]+)"
EXTRACT-timestamp = "timestamp":"(?<event_timestamp>[^"]+)"
```

## Dashboard Configuration

### Custom Dashboard Settings

#### Performance Tuning
```xml
<!-- In dashboard XML -->
<dashboard version="1.1">
  <label>Custom OPA Dashboard</label>
  <description>Customized OPA monitoring dashboard</description>
  
  <!-- Global refresh settings -->
  <refresh>30s</refresh>
  <refreshType>delay</refreshType>
  
  <!-- Custom search base -->
  <search id="base_search">
    <query>
      index=opa_decisions sourcetype="opa:decision" 
      | eval decision_time=strftime(_time, "%Y-%m-%d %H:%M:%S")
    </query>
    <earliest>-24h@h</earliest>
    <latest>now</latest>
  </search>
</dashboard>
```

### Dashboard Customization

#### Adding Custom Panels
1. Navigate to the dashboard in Splunk Web
2. Click **Edit** > **Add Panel**
3. Configure search and visualization
4. Save changes

#### Custom Searches for Panels
```spl
# Policy Decision Trends
index=opa_decisions sourcetype="opa:decision" 
| timechart span=1h count by result

# Top Denied Policies
index=opa_decisions sourcetype="opa:decision" result=false
| stats count by policy_name 
| sort -count 
| head 10

# User Activity Summary
index=opa_decisions sourcetype="opa:decision" 
| stats count, dc(policy_name) as unique_policies by user_context
| sort -count
```

## Alert Configuration

### Saved Search Alerts

#### Critical Policy Failures
```ini
[OPA - Critical Policy Failures]
search = index=opa_decisions sourcetype="opa:decision" result=false policy_name="critical_*" | stats count
dispatch.earliest_time = -5m
dispatch.latest_time = now
cron_schedule = */5 * * * *
alert.track = 1
alert.condition = search count > 10
action.email = 1
action.email.to = <EMAIL>
action.email.subject = Critical OPA Policy Failures Detected
```

#### OPA Instance Health
```ini
[OPA - Instance Health Check]
search = index=opa_health sourcetype="opa:health" status!="ok" | stats count by host
dispatch.earliest_time = -10m
dispatch.latest_time = now
cron_schedule = */10 * * * *
alert.track = 1
alert.condition = search count > 0
action.email = 1
action.email.to = <EMAIL>
```

### Custom Alert Actions

#### Webhook Integration
```ini
[OPA - Webhook Alert]
search = index=opa_decisions sourcetype="opa:decision" result=false
action.webhook = 1
action.webhook.param.url = https://hooks.slack.com/your-webhook-url
action.webhook.param.method = POST
action.webhook.param.body = {"text": "OPA Policy Violation: $result.policy_name$"}
```

## Advanced Configuration

### Performance Optimization

#### Input Optimization
```ini
# Optimize polling intervals based on data volume
[opa_decision_logs://high_volume]
polling_interval = 30
batch_size = 5000
max_concurrent_requests = 5

[opa_decision_logs://low_volume]
polling_interval = 300
batch_size = 100
max_concurrent_requests = 1
```

#### Index Optimization
```ini
# In indexes.conf
[opa_decisions]
homePath = $SPLUNK_DB/opa_decisions/db
coldPath = $SPLUNK_DB/opa_decisions/colddb
thawedPath = $SPLUNK_DB/opa_decisions/thaweddb
maxDataSize = auto_high_volume
maxHotBuckets = 10
maxWarmDBCount = 300
```

### Security Configuration

#### SSL/TLS Settings
```ini
[opa_decision_logs://secure_opa]
opa_endpoint = https://opa.example.com:8181
ssl_verify = true
ssl_cert_file = /path/to/client.crt
ssl_key_file = /path/to/client.key
ssl_ca_file = /path/to/ca.crt
```

#### Authentication
```ini
# Token-based authentication
[styra_das_audit://secure_das]
das_endpoint = https://api.styra.com
api_token = $encrypted_token$
auth_method = bearer

# Basic authentication
[opa_decision_logs://basic_auth]
opa_endpoint = https://opa.example.com:8181
username = opa_user
password = $encrypted_password$
auth_method = basic
```

## Data Model Configuration

### CIM Compliance

#### Authentication Data Model
```ini
[Authentication_OPA]
acceleration = 0
search = sourcetype="opa:decision" 
| eval action=if(result="true", "success", "failure")
| eval app=coalesce(service_name, "opa")
| eval dest=coalesce(opa_endpoint, "unknown")
| eval src=coalesce(client_ip, requested_by)
| eval user=coalesce(input_user, "unknown")
```

### Custom Data Models
Create custom data models for specific use cases:
1. Go to **Settings** > **Data Models**
2. Click **New Data Model**
3. Define objects and fields based on your OPA data structure

## Troubleshooting Configuration

### Debug Mode
Enable debug logging for troubleshooting:
```ini
[opa_decision_logs://debug_instance]
log_level = DEBUG
debug_output = true
```

### Configuration Validation
```spl
# Check input status
| rest /services/data/inputs/opa_decision_logs 
| table title, disabled, eai:acl.app

# Verify field extractions
index=opa_decisions sourcetype="opa:decision" 
| fieldsummary 
| where count > 0
```

## Best Practices

### Configuration Management
1. Use version control for configuration files
2. Test changes in development environment first
3. Document all customizations
4. Regular backup of configurations

### Monitoring
1. Set up alerts for input failures
2. Monitor data ingestion rates
3. Track dashboard performance
4. Regular health checks of OPA instances

### Security
1. Use encrypted credentials
2. Implement least privilege access
3. Regular security audits
4. Monitor for unauthorized changes

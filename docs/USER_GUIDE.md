# OPA Policy Audit & Compliance Add-on - User Guide

## Overview

This guide helps users navigate and utilize the OPA Policy Audit & Compliance Add-on for monitoring, analyzing, and reporting on Open Policy Agent deployments.

## Getting Started

### Accessing the Add-on
1. Log into Splunk Web
2. Navigate to **Apps** > **OPA Policy Audit & Compliance Add-on**
3. You'll see the main navigation with dashboards and tools

### Main Components
- **OPA Policy Audit Overview** - Main monitoring dashboard
- **OPA Security Dashboard** - Security-focused analytics
- **Setup** - Configuration interface
- **Search & Reporting** - Custom search interface

## Dashboards

### 1. OPA Policy Audit Overview

#### Key Metrics Panel
- **Total Decisions**: Overall policy evaluation count
- **Success Rate**: Percentage of allowed decisions
- **Active Policies**: Number of unique policies evaluated
- **OPA Instances**: Count of monitored OPA instances

#### Decision Trends
- Time-series visualization of policy decisions
- Success vs. failure rates over time
- Peak usage periods identification

#### Top Policies
- Most frequently evaluated policies
- Policies with highest failure rates
- Recently modified policies

#### Instance Health
- OPA instance status overview
- Response time metrics
- Error rate monitoring

### 2. OPA Security Dashboard

#### Security Metrics
- **Failed Authorizations**: Count of denied access attempts
- **Suspicious Activity**: Unusual patterns or behaviors
- **Policy Violations**: Security policy breaches
- **User Activity**: Authentication and authorization patterns

#### Threat Detection
- Anomalous access patterns
- Repeated authorization failures
- Unusual resource access attempts
- Geographic access analysis (if available)

#### Compliance Monitoring
- Policy compliance status
- Audit trail completeness
- Regulatory requirement tracking
- Exception reporting

## Search Examples

### Basic Searches

#### View All OPA Decisions
```spl
index=opa_decisions sourcetype="opa:decision"
| table _time, policy_name, result, user_context, resource_path
| sort -_time
```

#### Failed Authorization Attempts
```spl
index=opa_decisions sourcetype="opa:decision" result=false
| stats count by policy_name, user_context
| sort -count
```

#### OPA Instance Health Status
```spl
index=opa_health sourcetype="opa:health"
| table _time, host, status, response_time
| sort -_time
```

### Advanced Analytics

#### Policy Performance Analysis
```spl
index=opa_decisions sourcetype="opa:decision"
| eval response_time_ms = response_time * 1000
| stats avg(response_time_ms) as avg_response, 
        max(response_time_ms) as max_response,
        count as total_decisions
  by policy_name
| sort -total_decisions
```

#### User Behavior Analysis
```spl
index=opa_decisions sourcetype="opa:decision"
| stats count as total_requests,
        sum(eval(if(result="true", 1, 0))) as allowed,
        sum(eval(if(result="false", 1, 0))) as denied
  by user_context
| eval success_rate = round((allowed/total_requests)*100, 2)
| sort -total_requests
```

#### Geographic Access Patterns
```spl
index=opa_decisions sourcetype="opa:decision"
| iplocation client_ip
| stats count by Country, City, user_context
| geostats count by Country
```

#### Time-based Analysis
```spl
index=opa_decisions sourcetype="opa:decision"
| eval hour = strftime(_time, "%H")
| eval day_of_week = strftime(_time, "%A")
| stats count by hour, day_of_week, result
| xyseries hour day_of_week count
```

### Security Investigations

#### Detect Brute Force Attempts
```spl
index=opa_decisions sourcetype="opa:decision" result=false
| bucket _time span=5m
| stats count by _time, user_context, client_ip
| where count > 10
| sort -count
```

#### Identify Privilege Escalation
```spl
index=opa_decisions sourcetype="opa:decision"
| eval is_admin_action = if(match(resource_path, "admin|root|sudo"), 1, 0)
| where is_admin_action=1 AND result=true
| stats count by user_context, resource_path
| sort -count
```

#### Monitor Policy Changes
```spl
index=styra_audit sourcetype="styra:das:policy:audit" action_type="policy_update"
| table _time, user_id, policy_id, change_description
| sort -_time
```

## Reporting

### Standard Reports

#### Daily Security Summary
```spl
index=opa_decisions sourcetype="opa:decision" earliest=-24h@h latest=@h
| stats count as total_decisions,
        sum(eval(if(result="false", 1, 0))) as failed_decisions,
        dc(user_context) as unique_users,
        dc(policy_name) as unique_policies
| eval failure_rate = round((failed_decisions/total_decisions)*100, 2)
| table total_decisions, failed_decisions, failure_rate, unique_users, unique_policies
```

#### Weekly Policy Performance
```spl
index=opa_decisions sourcetype="opa:decision" earliest=-7d@d latest=@d
| eval day = strftime(_time, "%Y-%m-%d")
| stats count as decisions,
        avg(response_time) as avg_response_time
  by day, policy_name
| sort day, policy_name
```

#### Monthly Compliance Report
```spl
index=opa_decisions sourcetype="opa:decision" earliest=-30d@d latest=@d
| eval compliance_status = case(
    result="true" AND match(policy_name, "compliance_.*"), "Compliant",
    result="false" AND match(policy_name, "compliance_.*"), "Non-Compliant",
    1=1, "Not Applicable"
)
| stats count by compliance_status, policy_name
| sort compliance_status, -count
```

### Custom Report Creation

#### Creating Scheduled Reports
1. Run your search query
2. Click **Save As** > **Report**
3. Configure scheduling options
4. Set up email delivery if needed

#### Report Templates
```spl
# Executive Summary Template
index=opa_decisions sourcetype="opa:decision" earliest=-24h@h latest=@h
| stats count as "Total Decisions",
        sum(eval(if(result="true", 1, 0))) as "Successful",
        sum(eval(if(result="false", 1, 0))) as "Failed",
        dc(user_context) as "Unique Users"
| eval "Success Rate %" = round((Successful/Total_Decisions)*100, 2)
| table "Total Decisions", "Successful", "Failed", "Success Rate %", "Unique Users"
```

## Alerting

### Pre-configured Alerts

#### High Failure Rate Alert
- **Trigger**: When policy failure rate exceeds 20% in 15 minutes
- **Action**: Email notification to security team
- **Frequency**: Every 15 minutes

#### OPA Instance Down
- **Trigger**: When OPA health check fails
- **Action**: Email and Slack notification
- **Frequency**: Every 5 minutes

#### Suspicious User Activity
- **Trigger**: User with >50 failed authorizations in 1 hour
- **Action**: Email to security team with user details
- **Frequency**: Every hour

### Creating Custom Alerts

#### Step-by-Step Alert Creation
1. Create and test your search query
2. Click **Save As** > **Alert**
3. Configure trigger conditions
4. Set up notification actions
5. Test the alert

#### Alert Best Practices
- Use specific search criteria
- Set appropriate time windows
- Avoid alert fatigue with proper thresholds
- Include relevant context in notifications

## Data Analysis Techniques

### Statistical Analysis

#### Trend Analysis
```spl
index=opa_decisions sourcetype="opa:decision"
| timechart span=1h count by result
| predict count as predicted_count
```

#### Correlation Analysis
```spl
index=opa_decisions sourcetype="opa:decision"
| eval hour = strftime(_time, "%H")
| stats count by hour, result
| eval failure_rate = if(result="false", count, 0)
| stats sum(count) as total, sum(failure_rate) as failures by hour
| eval failure_percentage = (failures/total)*100
```

### Machine Learning Applications

#### Anomaly Detection
```spl
index=opa_decisions sourcetype="opa:decision"
| timechart span=1h count by user_context
| anomalydetection user_context
```

#### Clustering Analysis
```spl
index=opa_decisions sourcetype="opa:decision"
| stats count by user_context, policy_name
| kmeans k=5 user_context policy_name
```

## Best Practices

### Search Optimization
1. Use specific time ranges
2. Filter early in searches
3. Use summary indexing for frequent searches
4. Leverage data models when available

### Dashboard Usage
1. Customize dashboards for your use case
2. Use appropriate refresh intervals
3. Share dashboards with relevant teams
4. Regular review and updates

### Security Monitoring
1. Monitor for unusual patterns
2. Set up comprehensive alerting
3. Regular security reviews
4. Document investigation procedures

## Troubleshooting

### Common Issues

#### No Data in Dashboards
1. Check data input configuration
2. Verify index permissions
3. Confirm time range settings
4. Check search syntax

#### Slow Dashboard Performance
1. Optimize search queries
2. Use summary indexing
3. Adjust refresh intervals
4. Consider data retention policies

#### Alert Not Triggering
1. Test search manually
2. Check alert conditions
3. Verify scheduling settings
4. Review alert history

### Getting Help
- Check Splunk documentation
- Review add-on logs
- Contact system administrator
- Submit support tickets

## Advanced Features

### API Integration
- REST API endpoints for programmatic access
- Custom integrations with external systems
- Automated reporting capabilities

### Custom Visualizations
- Create custom dashboard panels
- Integrate third-party visualization tools
- Export data for external analysis

### Data Export
- Export search results to CSV/JSON
- Schedule automated exports
- Integration with external systems

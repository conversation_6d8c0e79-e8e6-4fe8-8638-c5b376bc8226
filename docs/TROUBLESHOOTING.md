# OPA Policy Audit & Compliance Add-on - Troubleshooting Guide

## Overview

This guide provides solutions to common issues encountered when using the OPA Policy Audit & Compliance Add-on.

## Quick Diagnostics

### Health Check Commands
```bash
# Check Splunk service status
$SPLUNK_HOME/bin/splunk status

# Verify add-on installation
$SPLUNK_HOME/bin/splunk list app

# Check input status
$SPLUNK_HOME/bin/splunk list inputstatus
```

### Diagnostic Searches
```spl
# Check for recent data ingestion
index=opa_* earliest=-1h | stats count by index, sourcetype

# Verify input errors
index=_internal source=*splunkd.log* "opa" ERROR | head 20

# Check Python script execution
index=_internal source=*python.log* "opa" | head 20
```

## Common Issues and Solutions

### 1. No Data Ingestion

#### Symptoms
- Empty dashboards
- No search results for OPA data
- Missing data in indexes

#### Diagnosis
```spl
# Check if inputs are enabled
| rest /services/data/inputs/opa_decision_logs 
| table title, disabled, eai:acl.app

# Look for input errors
index=_internal source=*splunkd.log* "opa" (ERROR OR WARN)
| table _time, component, message
| sort -_time
```

#### Solutions

**Check Input Configuration**
1. Navigate to **Settings** > **Data Inputs**
2. Verify OPA inputs are enabled
3. Check endpoint URLs and credentials
4. Test connectivity to OPA instances

**Network Connectivity**
```bash
# Test OPA endpoint connectivity
curl -v http://your-opa-instance:8181/health

# Check DNS resolution
nslookup your-opa-instance

# Verify firewall rules
telnet your-opa-instance 8181
```

**Credential Issues**
1. Verify API tokens are correct
2. Check authentication method
3. Ensure credentials have proper permissions
4. Test authentication manually

### 2. Authentication Failures

#### Symptoms
- HTTP 401/403 errors in logs
- "Authentication failed" messages
- Styra DAS connection issues

#### Diagnosis
```spl
index=_internal source=*splunkd.log* "opa" (401 OR 403 OR "auth")
| table _time, message
| sort -_time
```

#### Solutions

**API Token Issues**
1. Regenerate API tokens in Styra DAS
2. Update token in input configuration
3. Verify token permissions and scope
4. Check token expiration

**SSL/TLS Problems**
```ini
# Disable SSL verification temporarily for testing
[opa_decision_logs://test]
ssl_verify = false

# Use proper SSL configuration
[opa_decision_logs://production]
ssl_verify = true
ssl_cert_file = /path/to/cert.pem
ssl_ca_file = /path/to/ca.pem
```

### 3. Performance Issues

#### Symptoms
- Slow dashboard loading
- High CPU/memory usage
- Timeouts in searches
- Delayed data ingestion

#### Diagnosis
```spl
# Check search performance
index=_audit action=search 
| eval search_duration = total_run_time 
| where search_duration > 30 
| table _time, user, search, search_duration

# Monitor resource usage
| rest /services/server/info 
| table cpu_arch, numberOfCores, physicalMemoryMB

# Check input lag
| rest /services/data/inputs/opa_decision_logs 
| table title, interval, throughput
```

#### Solutions

**Optimize Polling Intervals**
```ini
# Reduce polling frequency for low-volume instances
[opa_decision_logs://low_volume]
polling_interval = 300

# Increase batch size for high-volume instances
[opa_decision_logs://high_volume]
batch_size = 5000
polling_interval = 60
```

**Search Optimization**
```spl
# Use specific time ranges
index=opa_decisions earliest=-1h latest=now

# Filter early in searches
index=opa_decisions sourcetype="opa:decision" result=false
| stats count by policy_name

# Use summary indexing for frequent searches
| collect index=summary_opa source="daily_summary"
```

**Resource Management**
1. Increase Splunk memory allocation
2. Optimize index configurations
3. Use data retention policies
4. Consider distributed deployment

### 4. Data Parsing Issues

#### Symptoms
- Missing fields in events
- Incorrect field extractions
- Malformed timestamps
- Wrong sourcetype assignment

#### Diagnosis
```spl
# Check field extraction
index=opa_decisions sourcetype="opa:decision" 
| fieldsummary 
| where count = 0

# Verify timestamp parsing
index=opa_decisions sourcetype="opa:decision" 
| eval time_diff = abs(_time - strptime(timestamp, "%Y-%m-%dT%H:%M:%S.%3N%z"))
| where time_diff > 60
```

#### Solutions

**Field Extraction Issues**
```ini
# Update props.conf for better field extraction
[opa:decision]
EXTRACT-policy_name = "policy":"(?<policy_name>[^"]+)"
EXTRACT-decision_result = "result":(?<decision_result>true|false)

# Test field extractions
| rex field=_raw "\"policy\":\"(?<test_policy>[^\"]+)\""
```

**Timestamp Problems**
```ini
# Fix timestamp configuration in props.conf
[opa:decision]
TIME_PREFIX = "timestamp":
TIME_FORMAT = %Y-%m-%dT%H:%M:%S.%3N%z
MAX_TIMESTAMP_LOOKAHEAD = 32
```

### 5. Dashboard and Visualization Issues

#### Symptoms
- Empty dashboard panels
- Visualization errors
- Incorrect data display
- Dashboard timeouts

#### Diagnosis
```spl
# Test dashboard searches manually
index=opa_decisions sourcetype="opa:decision" 
| timechart span=1h count by result

# Check for data in time range
index=opa_decisions earliest=-24h latest=now 
| stats count
```

#### Solutions

**Dashboard Configuration**
1. Verify search syntax in panels
2. Check time range settings
3. Update dashboard permissions
4. Refresh dashboard cache

**Visualization Problems**
1. Check data format requirements
2. Verify field names in searches
3. Update visualization settings
4. Test with sample data

### 6. Alert and Notification Issues

#### Symptoms
- Alerts not triggering
- Missing email notifications
- Incorrect alert frequency
- False positive alerts

#### Diagnosis
```spl
# Check alert history
index=_audit action=alert_fired 
| table _time, ss_name, result_count

# Verify alert search
| savedsearch "OPA - Critical Policy Failures"

# Check email configuration
| rest /services/admin/alert_actions/email
```

#### Solutions

**Alert Configuration**
1. Test alert search manually
2. Verify trigger conditions
3. Check scheduling settings
4. Update notification settings

**Email Setup**
```ini
# Configure email settings in alert_actions.conf
[email]
mailserver = smtp.company.com
use_ssl = 1
use_tls = 1
username = <EMAIL>
password = your_password
```

## Advanced Troubleshooting

### Debug Mode

#### Enable Debug Logging
```ini
# Add to inputs.conf
[opa_decision_logs://debug_instance]
log_level = DEBUG
debug_output = true
```

#### Python Script Debugging
```python
# Add to Python scripts
import logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)
logger.debug("Debug message here")
```

### Log Analysis

#### Key Log Locations
- Splunk main log: `$SPLUNK_HOME/var/log/splunk/splunkd.log`
- Python logs: `$SPLUNK_HOME/var/log/splunk/python.log`
- Input logs: Search `index=_internal source=*opa*`

#### Log Analysis Searches
```spl
# Find all OPA-related errors
index=_internal source=*splunkd.log* "opa" (ERROR OR WARN OR FATAL)
| table _time, component, message
| sort -_time

# Check Python script execution
index=_internal source=*python.log* "opa"
| table _time, message
| sort -_time

# Monitor input performance
index=_internal source=*metrics.log* group=per_source_thruput series="opa*"
| timechart avg(kb) by series
```

### Network Diagnostics

#### Connectivity Testing
```bash
# Test HTTP connectivity
curl -v -H "Authorization: Bearer YOUR_TOKEN" \
  https://opa.example.com:8181/v1/data/decisions

# Check SSL certificate
openssl s_client -connect opa.example.com:8181 -servername opa.example.com

# Test DNS resolution
dig opa.example.com
```

#### Firewall and Proxy Issues
1. Check corporate firewall rules
2. Verify proxy configuration
3. Test from Splunk server directly
4. Check SSL/TLS versions

### Performance Tuning

#### Index Optimization
```ini
# Optimize index settings
[opa_decisions]
maxDataSize = auto_high_volume
maxHotBuckets = 10
maxWarmDBCount = 300
```

#### Search Optimization
```spl
# Use tstats for better performance
| tstats count where index=opa_decisions by _time span=1h

# Leverage data models
| datamodel Authentication_OPA Authentication search
```

## Recovery Procedures

### Data Recovery

#### Backfill Missing Data
```spl
# Identify missing time periods
index=opa_decisions 
| bucket _time span=1h 
| stats count by _time 
| where count < expected_count
```

#### Manual Data Collection
1. Stop affected inputs
2. Collect missing data manually
3. Import via file monitoring
4. Restart inputs

### Configuration Recovery

#### Backup and Restore
```bash
# Backup configurations
tar -czf opa_addon_backup.tar.gz $SPLUNK_HOME/etc/apps/opa_policy_audit_addon/

# Restore from backup
tar -xzf opa_addon_backup.tar.gz -C $SPLUNK_HOME/etc/apps/
```

## Getting Help

### Internal Resources
1. Check Splunk documentation
2. Review add-on logs
3. Contact system administrator
4. Check knowledge base

### External Support
1. Splunk community forums
2. Vendor support channels
3. Professional services
4. Training resources

### Escalation Procedures
1. Document the issue thoroughly
2. Gather relevant logs and configurations
3. Provide steps to reproduce
4. Include environment details

## Prevention and Monitoring

### Proactive Monitoring
```spl
# Monitor input health
| rest /services/data/inputs/opa_decision_logs 
| eval status = if(disabled=0, "enabled", "disabled")
| table title, status, interval

# Check data ingestion rates
index=opa_* earliest=-24h 
| bucket _time span=1h 
| stats count by _time, index 
| timechart span=1h sum(count) by index
```

### Best Practices
1. Regular health checks
2. Monitor resource usage
3. Keep configurations backed up
4. Document customizations
5. Test changes in development
6. Maintain update schedules

### Maintenance Tasks
- Weekly: Review error logs
- Monthly: Check performance metrics
- Quarterly: Update configurations
- Annually: Review and optimize setup
